{"ast": null, "code": "var _jsxFileName = \"E:\\\\Uroom\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\BookingCheckPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, use } from \"react\";\nimport { Container, Row, Col, Card, Form, Button, InputGroup } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\nimport Banner from \"../../../images/banner.jpg\";\nimport Header from \"../Header\";\nimport Footer from \"../Footer\";\nimport * as Routers from \"../../../utils/Routes\";\nimport { useNavigate } from \"react-router-dom\";\nimport ConfirmationModal from \"@components/ConfirmationModal\";\nimport PromotionModal from \"./components/PromotionModal\";\nimport PromotionErrorModal from \"./components/PromotionErrorModal\";\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\nimport { applyPromotion } from \"../../../redux/promotion/actions\";\nimport Utils from \"../../../utils/Utils\";\nimport Factories from \"../../../redux/search/factories\";\nimport { ChatBox } from \"./HomePage\";\nimport SearchActions from \"../../../redux/search/actions\";\nimport HotelActions from \"@redux/hotel/actions\";\nimport HotelClosedModal from \"./components/HotelClosedModal\";\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\nimport getApiBackendUrl from \"@utils/apiConfig\";\nimport RoomClosedModal from \"./components/RoomClosedModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookingCheckPage = () => {\n  _s();\n  var _hotelDetail$hotelNam, _hotelDetail$address;\n  const API_BASE_URL = getApiBackendUrl(); // Add this line\n\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\n  const [error, setError] = useState(null);\n  const Auth = useAppSelector(state => state.Auth.Auth);\n  const SearchInformation = useAppSelector(state => state.Search.SearchInformation);\n  const selectedRoomsTemps = useAppSelector(state => state.Search.selectedRooms);\n  const selectedServicesFromRedux = useAppSelector(state => state.Search.selectedServices);\n  const hotelDetailFromRedux = useAppSelector(state => state.Search.hotelDetail);\n\n  // Redux promotion selectors\n  const {\n    applyLoading: promotionApplyLoading,\n    applyError: promotionApplyError,\n    appliedPromotion\n  } = useAppSelector(state => state.Promotion);\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\n\n  // Promotion code state\n  const [promotionCode, setPromotionCode] = useState(\"\");\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\n  const [promotionId, setPromotionId] = useState(null);\n\n  // Add state for booking data\n  const [bookingData, setBookingData] = useState({\n    selectedRooms: selectedRoomsTemps || [],\n    selectedServices: selectedServicesFromRedux || [],\n    hotelDetail: hotelDetailFromRedux || null,\n    searchInfo: SearchInformation\n  });\n  const [dataRestored, setDataRestored] = useState(false);\n  const [isValidatingPromotion, setIsValidatingPromotion] = useState(false);\n  const [isCheckingHotelStatus, setIsCheckingHotelStatus] = useState(false);\n  const [isValidatingPromotionBeforeBooking, setIsValidatingPromotionBeforeBooking] = useState(false);\n  const [isInitialLoading, setIsInitialLoading] = useState(true);\n\n  // Restore data from sessionStorage stack when component mounts\n  useEffect(() => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      const currentBooking = bookingStack[bookingStack.length - 1];\n      setBookingData(currentBooking);\n\n      // Update Redux store with current data\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: currentBooking.selectedRooms,\n          selectedServices: currentBooking.selectedServices,\n          hotelDetail: currentBooking.hotelDetail\n        }\n      });\n    }\n    setDataRestored(true);\n    setIsInitialLoading(false);\n  }, [dispatch]);\n\n  // Load promotion info from sessionStorage AFTER booking data is restored\n  useEffect(() => {\n    if (dataRestored) {\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\n      if (promo) {\n        var _bookingData$hotelDet, _bookingData$selected;\n        // Check if this is a new booking (different hotel or rooms)\n        const currentHotelId = (_bookingData$hotelDet = bookingData.hotelDetail) === null || _bookingData$hotelDet === void 0 ? void 0 : _bookingData$hotelDet._id;\n        const savedHotelId = promo.hotelId;\n        const currentRoomsHash = JSON.stringify((_bookingData$selected = bookingData.selectedRooms) === null || _bookingData$selected === void 0 ? void 0 : _bookingData$selected.map(r => ({\n          roomId: r.room._id,\n          amount: r.amount\n        })).sort());\n        const savedRoomsHash = promo.roomsHash;\n        if (currentHotelId !== savedHotelId || currentRoomsHash !== savedRoomsHash) {\n          // This is a new booking, clear old promotion\n          sessionStorage.removeItem(\"promotionInfo\");\n          console.log(\"🆕 New booking detected, cleared old promotion\");\n          return;\n        }\n\n        // Check if promotion was saved more than 5 minutes ago\n        const savedTime = promo.savedTime || Date.now();\n        const timeDiff = Date.now() - savedTime;\n        const fiveMinutes = 5 * 60 * 1000;\n        if (timeDiff > fiveMinutes) {\n          // Auto-validate if promotion is old\n          console.log(\"Promotion is old, auto-validating...\");\n          setPromotionCode(promo.promotionCode || \"\");\n          setPromotionDiscount(promo.promotionDiscount || 0);\n          setPromotionMessage(\"Validating promotion...\");\n          setPromotionId(promo.promotionId || null);\n        } else {\n          setPromotionCode(promo.promotionCode || \"\");\n          setPromotionDiscount(promo.promotionDiscount || 0);\n          setPromotionMessage(promo.promotionMessage || \"\");\n          setPromotionId(promo.promotionId || null);\n          console.log(\"🔄 Restored promotion for same booking:\", promo.promotionCode);\n        }\n      }\n    }\n  }, [dataRestored, bookingData.hotelDetail, bookingData.selectedRooms]);\n\n  // Save promotion info to sessionStorage when any promotion state changes\n  useEffect(() => {\n    if (dataRestored) {\n      var _bookingData$hotelDet2, _bookingData$selected2;\n      // Chỉ save khi đã restore xong data\n      sessionStorage.setItem(\"promotionInfo\", JSON.stringify({\n        promotionCode,\n        promotionDiscount,\n        promotionMessage,\n        promotionId,\n        savedTime: Date.now(),\n        // Add timestamp for validation\n        // Save booking context to detect new bookings\n        hotelId: (_bookingData$hotelDet2 = bookingData.hotelDetail) === null || _bookingData$hotelDet2 === void 0 ? void 0 : _bookingData$hotelDet2._id,\n        roomsHash: JSON.stringify((_bookingData$selected2 = bookingData.selectedRooms) === null || _bookingData$selected2 === void 0 ? void 0 : _bookingData$selected2.map(r => ({\n          roomId: r.room._id,\n          amount: r.amount\n        })).sort())\n      }));\n    }\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored, bookingData.hotelDetail, bookingData.selectedRooms]);\n\n  // Use bookingData instead of Redux state\n  const selectedRooms = bookingData.selectedRooms;\n  const selectedServices = bookingData.selectedServices;\n  const hotelDetail = bookingData.hotelDetail;\n  const searchInfo = bookingData.searchInfo;\n\n  // Calculate number of days between check-in and check-out\n  const calculateNumberOfDays = () => {\n    const checkIn = new Date(searchInfo.checkinDate);\n    const checkOut = new Date(searchInfo.checkoutDate);\n    const diffTime = Math.abs(checkOut - checkIn);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n  const numberOfDays = calculateNumberOfDays();\n\n  // Calculate prices\n  const totalRoomPrice = selectedRooms.reduce((total, {\n    room,\n    amount\n  }) => total + room.price * amount * numberOfDays, 0);\n  const totalServicePrice = selectedServices.reduce((total, service) => {\n    const selectedDates = service.selectedDates || [];\n    const serviceQuantity = service.quantity * selectedDates.length;\n    return total + service.price * serviceQuantity;\n  }, 0);\n  const subtotal = totalRoomPrice + totalServicePrice;\n  const finalPrice = Math.max(subtotal - promotionDiscount, 0);\n\n  // Validate promotion when data is restored or booking changes\n  useEffect(() => {\n    if (!dataRestored || !promotionCode || !promotionId || promotionDiscount === 0) return;\n\n    // Add a small delay to ensure promotion is fully restored before validation\n    const timeoutId = setTimeout(() => {\n      // Only show loading if validation takes longer than 200ms\n      const loadingTimeoutId = setTimeout(() => {\n        setIsValidatingPromotion(true);\n      }, 200);\n      dispatch(applyPromotion({\n        code: promotionCode,\n        orderAmount: subtotal,\n        onSuccess: data => {\n          clearTimeout(loadingTimeoutId);\n          setIsValidatingPromotion(false);\n          if (!data.valid || data.discount !== promotionDiscount) {\n            // Batch update all promotion states to minimize re-renders\n            setTimeout(() => {\n              setPromotionCode(\"\");\n              setPromotionDiscount(0);\n              setPromotionMessage(\"Promotion is no longer valid due to booking changes\");\n              setPromotionId(null);\n              sessionStorage.removeItem(\"promotionInfo\");\n            }, 0);\n          }\n        },\n        onFailed: error => {\n          clearTimeout(loadingTimeoutId);\n          setIsValidatingPromotion(false);\n          // Batch update all promotion states to minimize re-renders\n          setTimeout(() => {\n            setPromotionCode(\"\");\n            setPromotionDiscount(0);\n            setPromotionMessage(\"Promotion is no longer valid\");\n            setPromotionId(null);\n            sessionStorage.removeItem(\"promotionInfo\");\n          }, 0);\n        }\n      }));\n    }, 100);\n    return () => clearTimeout(timeoutId);\n  }, [dataRestored, subtotal, promotionCode, promotionId, promotionDiscount, dispatch]); // Validate when subtotal changes or data is restored\n\n  // Handle navigation back to HomeDetailPage\n  const handleBackToHomeDetail = () => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      // Remove the current booking from stack\n      bookingStack.pop();\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n    }\n    // Don't clear promotion here - user might come back with same selection\n    // Promotion will be cleared only when new booking (different hotel/rooms) is detected\n    navigate(-1);\n  };\n\n  // Star rating component\n  const StarRating = ({\n    rating\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"star-rating\",\n      children: [...Array(5)].map((_, index) => index < rating ? /*#__PURE__*/_jsxDEV(FaStar, {\n        className: \"star filled\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(FaRegStar, {\n        className: \"star\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this);\n  };\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\n  const [showPromotionErrorModal, setShowPromotionErrorModal] = useState(false);\n  const [promotionErrorMessage, setPromotionErrorMessage] = useState(\"\");\n  const [invalidPromotionCode, setInvalidPromotionCode] = useState(\"\");\n\n  // Add state for payment error modal\n  const [showPaymentErrorModal, setShowPaymentErrorModal] = useState(false);\n  const [paymentErrorMessage, setPaymentErrorMessage] = useState({\n    title: \"\",\n    mainMessage: \"\",\n    subMessage: \"\"\n  });\n\n  // Hàm xử lý áp dụng promotion từ modal với batch update để tránh multiple re-renders\n  const handleApplyPromotionFromModal = promotionData => {\n    // Batch update all promotion states at once to minimize re-renders\n    const updatePromotionStates = () => {\n      setPromotionCode(promotionData.code);\n      setPromotionDiscount(promotionData.discount);\n      setPromotionMessage(promotionData.message);\n      setPromotionId(promotionData.promotionId);\n    };\n\n    // Use setTimeout to batch the state updates\n    setTimeout(updatePromotionStates, 0);\n  };\n\n  // Function to validate promotion before booking (optimized to avoid unnecessary re-renders)\n  const validatePromotionBeforeBooking = async () => {\n    if (!promotionCode || !promotionId || promotionDiscount === 0) {\n      return {\n        valid: true\n      }; // No promotion to validate\n    }\n\n    // Only show loading state if validation takes longer than 300ms\n    let shouldShowLoading = false;\n    const loadingTimeoutId = setTimeout(() => {\n      shouldShowLoading = true;\n      setIsValidatingPromotionBeforeBooking(true);\n    }, 300);\n    return new Promise(resolve => {\n      dispatch(applyPromotion({\n        code: promotionCode,\n        orderAmount: subtotal,\n        onSuccess: data => {\n          // Clear timeout and loading state\n          clearTimeout(loadingTimeoutId);\n          if (shouldShowLoading) {\n            setIsValidatingPromotionBeforeBooking(false);\n          }\n          if (!data.valid) {\n            resolve({\n              valid: false,\n              message: data.message || \"Promotion is no longer valid\"\n            });\n            return;\n          }\n          if (data.discount !== promotionDiscount) {\n            resolve({\n              valid: false,\n              message: \"Promotion discount has changed. Please reapply the promotion.\"\n            });\n            return;\n          }\n          resolve({\n            valid: true\n          });\n        },\n        onFailed: error => {\n          // Clear timeout and loading state\n          clearTimeout(loadingTimeoutId);\n          if (shouldShowLoading) {\n            setIsValidatingPromotionBeforeBooking(false);\n          }\n          resolve({\n            valid: false,\n            message: \"Unable to validate promotion. Please try again.\"\n          });\n        }\n      }));\n    });\n  };\n\n  // Function to check hotel status before booking (optimized to avoid unnecessary loading states)\n  const checkHotelStatusBeforeBooking = async () => {\n    return new Promise((resolve, reject) => {\n      // Only show loading state if check takes longer than 300ms\n      let shouldShowLoading = false;\n      const loadingTimeoutId = setTimeout(() => {\n        shouldShowLoading = true;\n        setIsCheckingHotelStatus(true);\n      }, 300);\n      dispatch({\n        type: HotelActions.FETCH_DETAIL_HOTEL,\n        payload: {\n          hotelId: hotelDetail._id,\n          userId: Auth._id,\n          onSuccess: hotel => {\n            clearTimeout(loadingTimeoutId);\n            if (shouldShowLoading) {\n              setIsCheckingHotelStatus(false);\n            }\n            if (hotel.ownerStatus === \"ACTIVE\") {\n              resolve(hotel);\n            } else {\n              reject(new Error(\"Hotel is currently inactive\"));\n            }\n          },\n          onFailed: error => {\n            clearTimeout(loadingTimeoutId);\n            if (shouldShowLoading) {\n              setIsCheckingHotelStatus(false);\n            }\n            reject(new Error(error || \"Failed to check hotel status\"));\n          },\n          onError: () => {\n            clearTimeout(loadingTimeoutId);\n            if (shouldShowLoading) {\n              setIsCheckingHotelStatus(false);\n            }\n            reject(new Error(\"Server error while checking hotel status\"));\n          }\n        }\n      });\n    });\n  };\n  const createBooking = async () => {\n    try {\n      // Validate promotion first if there's one applied\n      const promotionValidation = await validatePromotionBeforeBooking();\n      if (!promotionValidation.valid) {\n        // Store error info for modal\n        setPromotionErrorMessage(promotionValidation.message);\n        setInvalidPromotionCode(promotionCode);\n\n        // Clear invalid promotion\n        setPromotionCode(\"\");\n        setPromotionDiscount(0);\n        setPromotionMessage(\"\");\n        setPromotionId(null);\n        sessionStorage.removeItem(\"promotionInfo\");\n\n        // Show error modal\n        setShowPromotionErrorModal(true);\n        return;\n      }\n\n      // Check hotel status\n      const hotel = await checkHotelStatusBeforeBooking();\n      console.log(\"Hotel detail fetched successfully:\", hotel);\n      const totalRoomPrice = selectedRooms.reduce((total, {\n        room,\n        amount\n      }) => total + room.price * amount * numberOfDays, 0);\n      const totalServicePrice = selectedServices.reduce((total, service) => {\n        const selectedDates = service.selectedDates || [];\n        const serviceQuantity = service.quantity * selectedDates.length;\n        return total + service.price * serviceQuantity;\n      }, 0);\n      const bookingSubtotal = totalRoomPrice + totalServicePrice;\n      const params = {\n        hotelId: hotelDetail._id,\n        checkOutDate: searchInfo.checkoutDate,\n        checkInDate: searchInfo.checkinDate,\n        totalPrice: bookingSubtotal,\n        // giá gốc\n        finalPrice: finalPrice,\n        // giá sau giảm giá\n        roomDetails: selectedRooms.map(({\n          room,\n          amount\n        }) => ({\n          room: {\n            _id: room._id\n          },\n          amount: amount\n        })),\n        serviceDetails: selectedServices.map(service => {\n          var _service$selectedDate;\n          return {\n            _id: service._id,\n            quantity: service.quantity * (((_service$selectedDate = service.selectedDates) === null || _service$selectedDate === void 0 ? void 0 : _service$selectedDate.length) || 0),\n            selectDate: service.selectedDates || []\n          };\n        }),\n        // Thêm promotionId và promotionDiscount nếu có\n        ...(promotionId && {\n          promotionId\n        }),\n        ...(promotionDiscount > 0 && {\n          promotionDiscount\n        })\n      };\n      console.log(\"params >> \", params);\n\n      // Helper function to save reservationId to bookingStack\n      const saveReservationIdToBookingStack = reservationId => {\n        if (reservationId) {\n          const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n          if (bookingStack.length > 0) {\n            bookingStack[bookingStack.length - 1].reservationId = reservationId;\n            sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n          }\n        }\n      };\n      try {\n        let reservationId = null;\n        const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n        if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\n          reservationId = bookingStack[bookingStack.length - 1].reservationId;\n        }\n        const response = await Factories.create_booking({\n          ...params,\n          reservationId\n        });\n        console.log(\"response >> \", response);\n        if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n          var _response$data, _response$data$unpaid, _responseCheckout$dat;\n          reservationId = response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : (_response$data$unpaid = _response$data.unpaidReservation) === null || _response$data$unpaid === void 0 ? void 0 : _response$data$unpaid._id;\n          saveReservationIdToBookingStack(reservationId);\n          const unpaidReservationId = reservationId;\n          const responseCheckout = await Factories.checkout_booking(unpaidReservationId);\n          console.log(\"responseCheckout >> \", responseCheckout);\n          const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat = responseCheckout.data) === null || _responseCheckout$dat === void 0 ? void 0 : _responseCheckout$dat.sessionUrl;\n          if (paymentUrl) {\n            // Don't clear promotion here - user might come back from payment\n            // Promotion will be cleared when new booking is created\n            window.location.href = paymentUrl;\n          }\n        } else if ((response === null || response === void 0 ? void 0 : response.status) === 201) {\n          var _response$data2, _response$data2$reser, _responseCheckout$dat2;\n          reservationId = response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : (_response$data2$reser = _response$data2.reservation) === null || _response$data2$reser === void 0 ? void 0 : _response$data2$reser._id;\n          saveReservationIdToBookingStack(reservationId);\n          const responseCheckout = await Factories.checkout_booking(reservationId);\n          const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat2 = responseCheckout.data) === null || _responseCheckout$dat2 === void 0 ? void 0 : _responseCheckout$dat2.sessionUrl;\n          if (paymentUrl) {\n            // Don't clear promotion here - user might come back from payment\n            // Promotion will be cleared when new booking is created\n            window.location.href = paymentUrl;\n          }\n        } else {\n          showToast.error(\"error create booking\");\n        }\n      } catch (error) {\n        var _error$response, _error$response$data, _error$response2, _error$response2$data;\n        console.error(\"Error create payment: \", (_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message);\n        setPaymentErrorMessage({\n          title: \"Payment Error\",\n          mainMessage: \"Unable to process your payment at this time\",\n          subMessage: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Please try again later\"\n        });\n        setShowPaymentErrorModal(true);\n      }\n    } catch (error) {\n      console.error(\"Error checking hotel status:\", error);\n      setShowModalStatusBooking(true);\n    }\n  };\n  const handleAccept = async () => {\n    const totalRoomPrice = selectedRooms.reduce((total, {\n      room,\n      amount\n    }) => total + room.price * amount * numberOfDays, 0);\n    if (totalRoomPrice > 0) {\n      // Final validation before creating booking\n      await createBooking();\n\n      // Only clear selection if booking was successful\n      // (createBooking will handle errors and not reach this point if failed)\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: [],\n          selectedServices: [],\n          hotelDetail: hotelDetail\n        }\n      });\n    }\n  };\n  const handleConfirmBooking = () => {\n    setShowAcceptModal(true);\n  };\n\n  // Only show loading spinner during initial load, not during re-renders\n  if (isInitialLoading || !hotelDetail && !dataRestored) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: \"100vh\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If data is restored but hotelDetail is still missing, redirect back\n  if (!hotelDetail && dataRestored) {\n    navigate(-1);\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex flex-column min-vh-100\",\n    style: {\n      backgroundImage: `url(${Banner})`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\",\n      style: {\n        paddingTop: \"65px\",\n        paddingBottom: \"50px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Container, {\n        className: \"mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"booking-card text-white\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                marginBottom: \"20px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stars mb-2\",\n                style: {\n                  justifyContent: \"flex-start\",\n                  justifyItems: \"self-start\"\n                },\n                children: /*#__PURE__*/_jsxDEV(StarRating, {\n                  rating: hotelDetail.star\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"hotel-name mb-1\",\n                children: (_hotelDetail$hotelNam = hotelDetail.hotelName) !== null && _hotelDetail$hotelNam !== void 0 ? _hotelDetail$hotelNam : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hotel-address small mb-4\",\n                children: (_hotelDetail$address = hotelDetail.address) !== null && _hotelDetail$address !== void 0 ? _hotelDetail$address : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-4\",\n                children: \"Your booking detail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkin\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkinDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 690,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 683,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkout\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkout\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 697,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkoutDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 703,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 696,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stay-info mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total length of stay:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [numberOfDays, \" night\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 713,\n                    columnNumber: 21\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total number of people:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [searchInfo.adults, \" Adults - \", searchInfo.childrens, \" \", \"Childrens\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-room mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-4\",\n                  children: \"You selected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 19\n                }, this), selectedRooms.map(({\n                  room,\n                  amount\n                }) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [amount, \" x \", room.name, \" (\", numberOfDays, \" days):\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: Utils.formatCurrency(room.price * amount * numberOfDays)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 744,\n                    columnNumber: 23\n                  }, this)]\n                }, room._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 21\n                }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: handleBackToHomeDetail,\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 753,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 752,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 17\n              }, this), selectedServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-services mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-3\",\n                  children: \"Selected Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 21\n                }, this), selectedServices.map(service => {\n                  const selectedDates = service.selectedDates || [];\n                  const serviceQuantity = service.quantity * selectedDates.length;\n                  const serviceTotal = service.price * serviceQuantity;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [service.quantity, \" x \", service.name, \" (\", selectedDates.length, \" days):\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 779,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"fw-bold\",\n                      children: Utils.formatCurrency(serviceTotal)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 783,\n                      columnNumber: 27\n                    }, this)]\n                  }, service._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 775,\n                    columnNumber: 25\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: () => {\n                      dispatch({\n                        type: SearchActions.SAVE_SELECTED_ROOMS,\n                        payload: {\n                          selectedRooms: selectedRooms,\n                          selectedServices: selectedServices,\n                          hotelDetail: hotelDetail\n                        }\n                      });\n                      navigate(-1);\n                    },\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 791,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-section mb-3\",\n                children: [promotionDiscount > 0 ? /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"promotion-applied mb-3\",\n                  style: {\n                    backgroundColor: \"rgba(40, 167, 69, 0.2)\",\n                    borderColor: \"#28a745\",\n                    border: \"2px solid #28a745\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"py-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                            className: \"text-success me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 836,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"fw-bold text-success\",\n                            children: promotionCode\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 837,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 835,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-success\",\n                          children: [\"Save \", Utils.formatCurrency(promotionDiscount)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 841,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 834,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-danger\",\n                        size: \"sm\",\n                        onClick: () => handleApplyPromotionFromModal({\n                          code: \"\",\n                          discount: 0,\n                          message: \"\",\n                          promotionId: null\n                        }),\n                        className: \"d-flex align-items-center\",\n                        disabled: isValidatingPromotion || isValidatingPromotionBeforeBooking,\n                        children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 862,\n                          columnNumber: 29\n                        }, this), isValidatingPromotion || isValidatingPromotionBeforeBooking ? \"...\" : \"Remove\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 845,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 833,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-3 mb-3\",\n                  style: {\n                    border: \"2px dashed rgba(255,255,255,0.3)\",\n                    borderRadius: \"8px\",\n                    backgroundColor: \"rgba(255,255,255,0.05)\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"text-muted mb-2\",\n                    size: 24\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 880,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-muted small\",\n                    children: \"No promotion applied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 881,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 872,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-light\",\n                  className: \"w-100 d-flex align-items-center justify-content-center\",\n                  onClick: () => setShowPromotionModal(true),\n                  style: {\n                    borderStyle: \"dashed\",\n                    borderWidth: \"2px\",\n                    padding: \"12px\"\n                  },\n                  disabled: isValidatingPromotion || isValidatingPromotionBeforeBooking,\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 902,\n                    columnNumber: 21\n                  }, this), isValidatingPromotion || isValidatingPromotionBeforeBooking ? \"Validating...\" : promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 19\n                }, this), (isValidatingPromotion || isValidatingPromotionBeforeBooking) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mt-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"spinner-border spinner-border-sm me-1\",\n                      role: \"status\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"visually-hidden\",\n                        children: \"Loading...\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 919,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 915,\n                      columnNumber: 25\n                    }, this), \"Checking promotion validity...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 913,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-breakdown\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Subtotal:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: Utils.formatCurrency(subtotal)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 929,\n                  columnNumber: 19\n                }, this), promotionDiscount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success\",\n                    children: \"Discount:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 938,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold text-success\",\n                    children: [\"-\", Utils.formatCurrency(promotionDiscount)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 939,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"booking-divider mb-2\",\n                  style: {\n                    height: \"1px\",\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                    margin: \"10px 0\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 945,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-danger mb-0\",\n                    children: [\"Total: \", Utils.formatCurrency(finalPrice)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 955,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 954,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small\",\n                  children: \"Includes taxes and fees\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 928,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                color: \"white\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-4\",\n                children: \"Check your information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Full name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 979,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    value: Auth.name,\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 980,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 978,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 992,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"email\",\n                    value: Auth.email,\n                    placeholder: \"<EMAIL>\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 993,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 991,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1006,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"tel\",\n                    value: Auth.phoneNumber,\n                    placeholder: \"0912345678\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1007,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1005,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Who are you booking for?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1020,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"mainGuest\",\n                      label: \"I'm the main guest\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"mainGuest\",\n                      onChange: () => setBookingFor(\"mainGuest\"),\n                      className: \"mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1022,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"someoneElse\",\n                      label: \"I'm booking for someone else\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"someoneElse\",\n                      onChange: () => setBookingFor(\"someoneElse\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1031,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1021,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1019,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    className: \"px-4 py-2\",\n                    style: {\n                      borderRadius: \"10px\",\n                      backgroundColor: \"white\",\n                      color: \"#007bff\",\n                      border: \"none\",\n                      fontWeight: \"bold\"\n                    },\n                    onClick: handleConfirmBooking,\n                    disabled: isCheckingHotelStatus || isValidatingPromotion || isValidatingPromotionBeforeBooking,\n                    children: isValidatingPromotionBeforeBooking ? \"Validating Promotion...\" : isCheckingHotelStatus ? \"Checking Hotel...\" : \"Booking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1043,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n                    show: showAcceptModal,\n                    onHide: () => setShowAcceptModal(false),\n                    onConfirm: handleAccept,\n                    title: \"Confirm Acceptance\",\n                    message: \"Do you want to proceed with this booking confirmation?\",\n                    confirmButtonText: \"Accept\",\n                    type: \"accept\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1066,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1042,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(ChatBox, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1082,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1081,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 634,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1085,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionModal, {\n      show: showPromotionModal,\n      onHide: () => setShowPromotionModal(false),\n      totalPrice: subtotal,\n      onApplyPromotion: handleApplyPromotionFromModal,\n      currentPromotionId: promotionId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1088,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HotelClosedModal, {\n      show: showModalStatusBooking,\n      onClose: () => {\n        setShowModalStatusBooking(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1096,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionErrorModal, {\n      show: showPromotionErrorModal,\n      onClose: () => {\n        setShowPromotionErrorModal(false);\n        setPromotionErrorMessage(\"\");\n        setInvalidPromotionCode(\"\");\n      },\n      onSelectNewPromotion: () => {\n        setShowPromotionErrorModal(false);\n        setPromotionErrorMessage(\"\");\n        setInvalidPromotionCode(\"\");\n        setShowPromotionModal(true);\n      },\n      errorMessage: promotionErrorMessage,\n      promotionCode: invalidPromotionCode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RoomClosedModal, {\n      show: showPaymentErrorModal,\n      onClose: () => setShowPaymentErrorModal(false),\n      title: paymentErrorMessage.title,\n      mainMessage: paymentErrorMessage.mainMessage,\n      subMessage: paymentErrorMessage.subMessage,\n      buttonText: \"Try Again\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 625,\n    columnNumber: 5\n  }, this);\n};\n_s(BookingCheckPage, \"oU4QCMgrSbzaFs1eZTRIJ/axa6M=\", false, function () {\n  return [useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useNavigate, useAppDispatch];\n});\n_c = BookingCheckPage;\nexport default BookingCheckPage;\nvar _c;\n$RefreshReg$(_c, \"BookingCheckPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "use", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "InputGroup", "FaStar", "FaRegStar", "FaTag", "FaTimes", "Banner", "Header", "Footer", "Routers", "useNavigate", "ConfirmationModal", "PromotionModal", "PromotionErrorModal", "useAppSelector", "useAppDispatch", "applyPromotion", "Utils", "Factories", "ChatBox", "SearchActions", "HotelActions", "HotelClosedModal", "showToast", "ToastProvider", "getApiBackendUrl", "RoomClosedModal", "jsxDEV", "_jsxDEV", "BookingCheckPage", "_s", "_hotelDetail$hotelNam", "_hotelDetail$address", "API_BASE_URL", "showModalStatusBooking", "setShowModalStatusBooking", "error", "setError", "<PERSON><PERSON>", "state", "SearchInformation", "Search", "selectedRoomsTemps", "selectedRooms", "selectedServicesFromRedux", "selectedServices", "hotelDetailFromRedux", "hotelDetail", "applyLoading", "promotionApplyLoading", "applyError", "promotionApplyError", "appliedPromotion", "Promotion", "navigate", "dispatch", "bookingFor", "setBookingFor", "promotionCode", "setPromotionCode", "promotionDiscount", "setPromotionDiscount", "promotionMessage", "setPromotionMessage", "promotionId", "setPromotionId", "bookingData", "setBookingData", "searchInfo", "dataRestored", "setDataRestored", "isValidatingPromotion", "setIsValidatingPromotion", "isCheckingHotelStatus", "setIsCheckingHotelStatus", "isValidatingPromotionBeforeBooking", "setIsValidatingPromotionBeforeBooking", "isInitialLoading", "setIsInitialLoading", "bookingStack", "JSON", "parse", "sessionStorage", "getItem", "length", "currentBooking", "type", "SAVE_SELECTED_ROOMS", "payload", "promo", "_bookingData$hotelDet", "_bookingData$selected", "currentHotelId", "_id", "savedHotelId", "hotelId", "currentRoomsHash", "stringify", "map", "r", "roomId", "room", "amount", "sort", "savedRoomsHash", "roomsHash", "removeItem", "console", "log", "savedTime", "Date", "now", "timeDiff", "fiveMinutes", "_bookingData$hotelDet2", "_bookingData$selected2", "setItem", "calculateNumberOfDays", "checkIn", "checkinDate", "checkOut", "checkoutDate", "diffTime", "Math", "abs", "diffDays", "ceil", "numberOfDays", "totalRoomPrice", "reduce", "total", "price", "totalServicePrice", "service", "selectedDates", "serviceQuantity", "quantity", "subtotal", "finalPrice", "max", "timeoutId", "setTimeout", "loadingTimeoutId", "code", "orderAmount", "onSuccess", "data", "clearTimeout", "valid", "discount", "onFailed", "handleBackToHomeDetail", "pop", "StarRating", "rating", "className", "children", "Array", "_", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showAcceptModal", "setShowAcceptModal", "showPromotionModal", "setShowPromotionModal", "showPromotionErrorModal", "setShowPromotionErrorModal", "promotionErrorMessage", "setPromotionErrorMessage", "invalidPromotionCode", "setInvalidPromotionCode", "showPaymentErrorModal", "setShowPaymentErrorModal", "paymentErrorMessage", "setPaymentErrorMessage", "title", "mainMessage", "subMessage", "handleApplyPromotionFromModal", "promotionData", "updatePromotionStates", "message", "validatePromotionBeforeBooking", "shouldShowLoading", "Promise", "resolve", "checkHotelStatusBeforeBooking", "reject", "FETCH_DETAIL_HOTEL", "userId", "hotel", "ownerStatus", "Error", "onError", "createBooking", "promotionValidation", "bookingSubtotal", "params", "checkOutDate", "checkInDate", "totalPrice", "roomDetails", "serviceDetails", "_service$selectedDate", "selectDate", "saveReservationIdToBookingStack", "reservationId", "response", "create_booking", "status", "_response$data", "_response$data$unpaid", "_responseCheckout$dat", "unpaidReservation", "unpaidReservationId", "responseCheckout", "checkout_booking", "paymentUrl", "sessionUrl", "window", "location", "href", "_response$data2", "_response$data2$reser", "_responseCheckout$dat2", "reservation", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "handleAccept", "handleConfirmBooking", "style", "height", "role", "backgroundImage", "backgroundSize", "backgroundPosition", "paddingTop", "paddingBottom", "md", "lg", "backgroundColor", "borderRadius", "padding", "marginBottom", "justifyContent", "justifyItems", "star", "hotelName", "address", "margin", "xs", "fontSize", "getDate", "adults", "childrens", "name", "formatCurrency", "cursor", "onClick", "serviceTotal", "borderColor", "border", "Body", "variant", "size", "disabled", "borderStyle", "borderWidth", "color", "Group", "Label", "Control", "value", "email", "placeholder", "phoneNumber", "Check", "id", "label", "checked", "onChange", "fontWeight", "show", "onHide", "onConfirm", "confirmButtonText", "onApplyPromotion", "currentPromotionId", "onClose", "onSelectNewPromotion", "errorMessage", "buttonText", "_c", "$RefreshReg$"], "sources": ["E:/Uroom/Customer/src/pages/customer/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect, use } from \"react\";\r\nimport {\r\n  Container,\r\n  Row,\r\n  Col,\r\n  Card,\r\n  Form,\r\n  Button,\r\n  InputGroup,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\r\nimport Banner from \"../../../images/banner.jpg\";\r\nimport Header from \"../Header\";\r\nimport Footer from \"../Footer\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport ConfirmationModal from \"@components/ConfirmationModal\";\r\nimport PromotionModal from \"./components/PromotionModal\";\r\nimport PromotionErrorModal from \"./components/PromotionErrorModal\";\r\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\r\nimport { applyPromotion } from \"../../../redux/promotion/actions\";\r\nimport Utils from \"../../../utils/Utils\";\r\nimport Factories from \"../../../redux/search/factories\";\r\nimport { ChatBox } from \"./HomePage\";\r\nimport SearchActions from \"../../../redux/search/actions\";\r\nimport HotelActions from \"@redux/hotel/actions\";\r\nimport HotelClosedModal from \"./components/HotelClosedModal\";\r\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\r\nimport getApiBackendUrl from \"@utils/apiConfig\";\r\nimport RoomClosedModal from \"./components/RoomClosedModal\";\r\n\r\nconst BookingCheckPage = () => {\r\n  const API_BASE_URL = getApiBackendUrl(); // Add this line\r\n\r\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\r\n  const [error, setError]= useState(null);\r\n  const Auth = useAppSelector((state) => state.Auth.Auth);\r\n  const SearchInformation = useAppSelector(\r\n    (state) => state.Search.SearchInformation\r\n  );\r\n\r\n  const selectedRoomsTemps = useAppSelector(\r\n    (state) => state.Search.selectedRooms\r\n  );\r\n  const selectedServicesFromRedux = useAppSelector(\r\n    (state) => state.Search.selectedServices\r\n  );\r\n  const hotelDetailFromRedux = useAppSelector(\r\n    (state) => state.Search.hotelDetail\r\n  );\r\n\r\n  // Redux promotion selectors\r\n  const {\r\n    applyLoading: promotionApplyLoading,\r\n    applyError: promotionApplyError,\r\n    appliedPromotion\r\n  } = useAppSelector(state => state.Promotion);\r\n\r\n  const navigate = useNavigate();\r\n  const dispatch = useAppDispatch();\r\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\r\n\r\n  // Promotion code state\r\n  const [promotionCode, setPromotionCode] = useState(\"\");\r\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\r\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\r\n  const [promotionId, setPromotionId] = useState(null);\r\n\r\n  // Add state for booking data\r\n  const [bookingData, setBookingData] = useState({\r\n    selectedRooms: selectedRoomsTemps || [],\r\n    selectedServices: selectedServicesFromRedux || [],\r\n    hotelDetail: hotelDetailFromRedux || null,\r\n    searchInfo: SearchInformation,\r\n  });\r\n\r\n  const [dataRestored, setDataRestored] = useState(false);\r\n  const [isValidatingPromotion, setIsValidatingPromotion] = useState(false);\r\n  const [isCheckingHotelStatus, setIsCheckingHotelStatus] = useState(false);\r\n  const [\r\n    isValidatingPromotionBeforeBooking,\r\n    setIsValidatingPromotionBeforeBooking,\r\n  ] = useState(false);\r\n  const [isInitialLoading, setIsInitialLoading] = useState(true);\r\n\r\n  // Restore data from sessionStorage stack when component mounts\r\n  useEffect(() => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      const currentBooking = bookingStack[bookingStack.length - 1];\r\n      setBookingData(currentBooking);\r\n\r\n      // Update Redux store with current data\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: currentBooking.selectedRooms,\r\n          selectedServices: currentBooking.selectedServices,\r\n          hotelDetail: currentBooking.hotelDetail,\r\n        },\r\n      });\r\n    }\r\n    setDataRestored(true);\r\n    setIsInitialLoading(false);\r\n  }, [dispatch]);\r\n\r\n  // Load promotion info from sessionStorage AFTER booking data is restored\r\n  useEffect(() => {\r\n    if (dataRestored) {\r\n      const promo = JSON.parse(\r\n        sessionStorage.getItem(\"promotionInfo\") || \"null\"\r\n      );\r\n      if (promo) {\r\n        // Check if this is a new booking (different hotel or rooms)\r\n        const currentHotelId = bookingData.hotelDetail?._id;\r\n        const savedHotelId = promo.hotelId;\r\n        const currentRoomsHash = JSON.stringify(\r\n          bookingData.selectedRooms\r\n            ?.map((r) => ({ roomId: r.room._id, amount: r.amount }))\r\n            .sort()\r\n        );\r\n        const savedRoomsHash = promo.roomsHash;\r\n\r\n        if (\r\n          currentHotelId !== savedHotelId ||\r\n          currentRoomsHash !== savedRoomsHash\r\n        ) {\r\n          // This is a new booking, clear old promotion\r\n          sessionStorage.removeItem(\"promotionInfo\");\r\n          console.log(\"🆕 New booking detected, cleared old promotion\");\r\n          return;\r\n        }\r\n\r\n        // Check if promotion was saved more than 5 minutes ago\r\n        const savedTime = promo.savedTime || Date.now();\r\n        const timeDiff = Date.now() - savedTime;\r\n        const fiveMinutes = 5 * 60 * 1000;\r\n\r\n        if (timeDiff > fiveMinutes) {\r\n          // Auto-validate if promotion is old\r\n          console.log(\"Promotion is old, auto-validating...\");\r\n          setPromotionCode(promo.promotionCode || \"\");\r\n          setPromotionDiscount(promo.promotionDiscount || 0);\r\n          setPromotionMessage(\"Validating promotion...\");\r\n          setPromotionId(promo.promotionId || null);\r\n        } else {\r\n          setPromotionCode(promo.promotionCode || \"\");\r\n          setPromotionDiscount(promo.promotionDiscount || 0);\r\n          setPromotionMessage(promo.promotionMessage || \"\");\r\n          setPromotionId(promo.promotionId || null);\r\n          console.log(\r\n            \"🔄 Restored promotion for same booking:\",\r\n            promo.promotionCode\r\n          );\r\n        }\r\n      }\r\n    }\r\n  }, [dataRestored, bookingData.hotelDetail, bookingData.selectedRooms]);\r\n\r\n  // Save promotion info to sessionStorage when any promotion state changes\r\n  useEffect(() => {\r\n    if (dataRestored) {\r\n      // Chỉ save khi đã restore xong data\r\n      sessionStorage.setItem(\r\n        \"promotionInfo\",\r\n        JSON.stringify({\r\n          promotionCode,\r\n          promotionDiscount,\r\n          promotionMessage,\r\n          promotionId,\r\n          savedTime: Date.now(), // Add timestamp for validation\r\n          // Save booking context to detect new bookings\r\n          hotelId: bookingData.hotelDetail?._id,\r\n          roomsHash: JSON.stringify(\r\n            bookingData.selectedRooms\r\n              ?.map((r) => ({ roomId: r.room._id, amount: r.amount }))\r\n              .sort()\r\n          ),\r\n        })\r\n      );\r\n    }\r\n  }, [\r\n    promotionCode,\r\n    promotionDiscount,\r\n    promotionMessage,\r\n    promotionId,\r\n    dataRestored,\r\n    bookingData.hotelDetail,\r\n    bookingData.selectedRooms,\r\n  ]);\r\n\r\n  // Use bookingData instead of Redux state\r\n  const selectedRooms = bookingData.selectedRooms;\r\n  const selectedServices = bookingData.selectedServices;\r\n  const hotelDetail = bookingData.hotelDetail;\r\n  const searchInfo = bookingData.searchInfo;\r\n\r\n  // Calculate number of days between check-in and check-out\r\n  const calculateNumberOfDays = () => {\r\n    const checkIn = new Date(searchInfo.checkinDate);\r\n    const checkOut = new Date(searchInfo.checkoutDate);\r\n    const diffTime = Math.abs(checkOut - checkIn);\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n    return diffDays;\r\n  };\r\n\r\n  const numberOfDays = calculateNumberOfDays();\r\n\r\n  // Calculate prices\r\n  const totalRoomPrice = selectedRooms.reduce(\r\n    (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n    0\r\n  );\r\n  const totalServicePrice = selectedServices.reduce((total, service) => {\r\n    const selectedDates = service.selectedDates || [];\r\n    const serviceQuantity = service.quantity * selectedDates.length;\r\n    return total + service.price * serviceQuantity;\r\n  }, 0);\r\n  const subtotal = totalRoomPrice + totalServicePrice;\r\n  const finalPrice = Math.max(subtotal - promotionDiscount, 0);\r\n\r\n  // Validate promotion when data is restored or booking changes\r\n  useEffect(() => {\r\n    if (\r\n      !dataRestored ||\r\n      !promotionCode ||\r\n      !promotionId ||\r\n      promotionDiscount === 0\r\n    )\r\n      return;\r\n\r\n    // Add a small delay to ensure promotion is fully restored before validation\r\n    const timeoutId = setTimeout(() => {\r\n      // Only show loading if validation takes longer than 200ms\r\n      const loadingTimeoutId = setTimeout(() => {\r\n        setIsValidatingPromotion(true);\r\n      }, 200);\r\n\r\n      dispatch(applyPromotion({\r\n        code: promotionCode,\r\n        orderAmount: subtotal,\r\n        onSuccess: (data) => {\r\n          clearTimeout(loadingTimeoutId);\r\n          setIsValidatingPromotion(false);\r\n\r\n          if (!data.valid || data.discount !== promotionDiscount) {\r\n            // Batch update all promotion states to minimize re-renders\r\n            setTimeout(() => {\r\n              setPromotionCode(\"\");\r\n              setPromotionDiscount(0);\r\n              setPromotionMessage(\r\n                \"Promotion is no longer valid due to booking changes\"\r\n              );\r\n              setPromotionId(null);\r\n              sessionStorage.removeItem(\"promotionInfo\");\r\n            }, 0);\r\n          }\r\n        },\r\n        onFailed: (error) => {\r\n          clearTimeout(loadingTimeoutId);\r\n          setIsValidatingPromotion(false);\r\n          // Batch update all promotion states to minimize re-renders\r\n          setTimeout(() => {\r\n            setPromotionCode(\"\");\r\n            setPromotionDiscount(0);\r\n            setPromotionMessage(\"Promotion is no longer valid\");\r\n            setPromotionId(null);\r\n            sessionStorage.removeItem(\"promotionInfo\");\r\n          }, 0);\r\n        }\r\n      }));\r\n    }, 100);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [dataRestored, subtotal, promotionCode, promotionId, promotionDiscount, dispatch]); // Validate when subtotal changes or data is restored\r\n\r\n  // Handle navigation back to HomeDetailPage\r\n  const handleBackToHomeDetail = () => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      // Remove the current booking from stack\r\n      bookingStack.pop();\r\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n    }\r\n    // Don't clear promotion here - user might come back with same selection\r\n    // Promotion will be cleared only when new booking (different hotel/rooms) is detected\r\n    navigate(-1);\r\n  };\r\n\r\n  // Star rating component\r\n  const StarRating = ({ rating }) => {\r\n    return (\r\n      <div className=\"star-rating\">\r\n        {[...Array(5)].map((_, index) =>\r\n          index < rating ? (\r\n            <FaStar key={index} className=\"star filled\" />\r\n          ) : (\r\n            <FaRegStar key={index} className=\"star\" />\r\n          )\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\r\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\r\n  const [showPromotionErrorModal, setShowPromotionErrorModal] = useState(false);\r\n  const [promotionErrorMessage, setPromotionErrorMessage] = useState(\"\");\r\n  const [invalidPromotionCode, setInvalidPromotionCode] = useState(\"\");\r\n\r\n  // Add state for payment error modal\r\n  const [showPaymentErrorModal, setShowPaymentErrorModal] = useState(false);\r\n  const [paymentErrorMessage, setPaymentErrorMessage] = useState({\r\n    title: \"\",\r\n    mainMessage: \"\",\r\n    subMessage: \"\"\r\n  });\r\n\r\n  // Hàm xử lý áp dụng promotion từ modal với batch update để tránh multiple re-renders\r\n  const handleApplyPromotionFromModal = (promotionData) => {\r\n    // Batch update all promotion states at once to minimize re-renders\r\n    const updatePromotionStates = () => {\r\n      setPromotionCode(promotionData.code);\r\n      setPromotionDiscount(promotionData.discount);\r\n      setPromotionMessage(promotionData.message);\r\n      setPromotionId(promotionData.promotionId);\r\n    };\r\n\r\n    // Use setTimeout to batch the state updates\r\n    setTimeout(updatePromotionStates, 0);\r\n  };\r\n\r\n  // Function to validate promotion before booking (optimized to avoid unnecessary re-renders)\r\n  const validatePromotionBeforeBooking = async () => {\r\n    if (!promotionCode || !promotionId || promotionDiscount === 0) {\r\n      return { valid: true }; // No promotion to validate\r\n    }\r\n\r\n    // Only show loading state if validation takes longer than 300ms\r\n    let shouldShowLoading = false;\r\n    const loadingTimeoutId = setTimeout(() => {\r\n      shouldShowLoading = true;\r\n      setIsValidatingPromotionBeforeBooking(true);\r\n    }, 300);\r\n\r\n    return new Promise((resolve) => {\r\n      dispatch(applyPromotion({\r\n        code: promotionCode,\r\n        orderAmount: subtotal,\r\n        onSuccess: (data) => {\r\n          // Clear timeout and loading state\r\n          clearTimeout(loadingTimeoutId);\r\n          if (shouldShowLoading) {\r\n            setIsValidatingPromotionBeforeBooking(false);\r\n          }\r\n\r\n          if (!data.valid) {\r\n            resolve({\r\n              valid: false,\r\n              message: data.message || \"Promotion is no longer valid\",\r\n            });\r\n            return;\r\n          }\r\n\r\n          if (data.discount !== promotionDiscount) {\r\n            resolve({\r\n              valid: false,\r\n              message:\r\n                \"Promotion discount has changed. Please reapply the promotion.\",\r\n            });\r\n            return;\r\n          }\r\n\r\n          resolve({ valid: true });\r\n        },\r\n        onFailed: (error) => {\r\n          // Clear timeout and loading state\r\n          clearTimeout(loadingTimeoutId);\r\n          if (shouldShowLoading) {\r\n            setIsValidatingPromotionBeforeBooking(false);\r\n          }\r\n          resolve({\r\n            valid: false,\r\n            message: \"Unable to validate promotion. Please try again.\",\r\n          });\r\n        }\r\n      }));\r\n    });\r\n  };\r\n\r\n  // Function to check hotel status before booking (optimized to avoid unnecessary loading states)\r\n  const checkHotelStatusBeforeBooking = async () => {\r\n    return new Promise((resolve, reject) => {\r\n      // Only show loading state if check takes longer than 300ms\r\n      let shouldShowLoading = false;\r\n      const loadingTimeoutId = setTimeout(() => {\r\n        shouldShowLoading = true;\r\n        setIsCheckingHotelStatus(true);\r\n      }, 300);\r\n\r\n      dispatch({\r\n        type: HotelActions.FETCH_DETAIL_HOTEL,\r\n        payload: {\r\n          hotelId: hotelDetail._id,\r\n          userId: Auth._id,\r\n          onSuccess: (hotel) => {\r\n            clearTimeout(loadingTimeoutId);\r\n            if (shouldShowLoading) {\r\n              setIsCheckingHotelStatus(false);\r\n            }\r\n            if (hotel.ownerStatus === \"ACTIVE\") {\r\n              resolve(hotel);\r\n            } else {\r\n              reject(new Error(\"Hotel is currently inactive\"));\r\n            }\r\n          },\r\n          onFailed: (error) => {\r\n            clearTimeout(loadingTimeoutId);\r\n            if (shouldShowLoading) {\r\n              setIsCheckingHotelStatus(false);\r\n            }\r\n            reject(new Error(error || \"Failed to check hotel status\"));\r\n          },\r\n          onError: () => {\r\n            clearTimeout(loadingTimeoutId);\r\n            if (shouldShowLoading) {\r\n              setIsCheckingHotelStatus(false);\r\n            }\r\n            reject(new Error(\"Server error while checking hotel status\"));\r\n          },\r\n        },\r\n      });\r\n    });\r\n  };\r\n  const createBooking = async () => {\r\n    try {\r\n      // Validate promotion first if there's one applied\r\n      const promotionValidation = await validatePromotionBeforeBooking();\r\n      if (!promotionValidation.valid) {\r\n        // Store error info for modal\r\n        setPromotionErrorMessage(promotionValidation.message);\r\n        setInvalidPromotionCode(promotionCode);\r\n\r\n        // Clear invalid promotion\r\n        setPromotionCode(\"\");\r\n        setPromotionDiscount(0);\r\n        setPromotionMessage(\"\");\r\n        setPromotionId(null);\r\n        sessionStorage.removeItem(\"promotionInfo\");\r\n\r\n        // Show error modal\r\n        setShowPromotionErrorModal(true);\r\n        return;\r\n      }\r\n\r\n      // Check hotel status\r\n      const hotel = await checkHotelStatusBeforeBooking();\r\n      console.log(\"Hotel detail fetched successfully:\", hotel);\r\n      const totalRoomPrice = selectedRooms.reduce(\r\n        (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n        0\r\n      );\r\n\r\n      const totalServicePrice = selectedServices.reduce((total, service) => {\r\n        const selectedDates = service.selectedDates || [];\r\n        const serviceQuantity = service.quantity * selectedDates.length;\r\n        return total + service.price * serviceQuantity;\r\n      }, 0);\r\n\r\n      const bookingSubtotal = totalRoomPrice + totalServicePrice;\r\n\r\n      const params = {\r\n        hotelId: hotelDetail._id,\r\n        checkOutDate: searchInfo.checkoutDate,\r\n        checkInDate: searchInfo.checkinDate,\r\n        totalPrice: bookingSubtotal, // giá gốc\r\n        finalPrice: finalPrice, // giá sau giảm giá\r\n        roomDetails: selectedRooms.map(({ room, amount }) => ({\r\n          room: {\r\n            _id: room._id,\r\n          },\r\n          amount: amount,\r\n        })),\r\n        serviceDetails: selectedServices.map((service) => ({\r\n          _id: service._id,\r\n          quantity: service.quantity * (service.selectedDates?.length || 0),\r\n          selectDate: service.selectedDates || [],\r\n        })),\r\n        // Thêm promotionId và promotionDiscount nếu có\r\n        ...(promotionId && { promotionId }),\r\n        ...(promotionDiscount > 0 && { promotionDiscount }),\r\n      };\r\n\r\n      console.log(\"params >> \", params);\r\n\r\n      // Helper function to save reservationId to bookingStack\r\n      const saveReservationIdToBookingStack = (reservationId) => {\r\n        if (reservationId) {\r\n          const bookingStack = JSON.parse(\r\n            sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n          );\r\n          if (bookingStack.length > 0) {\r\n            bookingStack[bookingStack.length - 1].reservationId = reservationId;\r\n            sessionStorage.setItem(\r\n              \"bookingStack\",\r\n              JSON.stringify(bookingStack)\r\n            );\r\n          }\r\n        }\r\n      };\r\n      try {\r\n        let reservationId = null;\r\n        const bookingStack = JSON.parse(\r\n          sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n        );\r\n        if (\r\n          bookingStack.length > 0 &&\r\n          bookingStack[bookingStack.length - 1].reservationId\r\n        ) {\r\n          reservationId = bookingStack[bookingStack.length - 1].reservationId;\r\n        }\r\n        const response = await Factories.create_booking({\r\n          ...params,\r\n          reservationId,\r\n        });\r\n        console.log(\"response >> \", response);\r\n        if (response?.status === 200) {\r\n          reservationId = response?.data?.unpaidReservation?._id;\r\n          saveReservationIdToBookingStack(reservationId);\r\n          const unpaidReservationId = reservationId;\r\n          const responseCheckout = await Factories.checkout_booking(\r\n            unpaidReservationId\r\n          );\r\n          console.log(\"responseCheckout >> \", responseCheckout);\r\n          const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n          if (paymentUrl) {\r\n            // Don't clear promotion here - user might come back from payment\r\n            // Promotion will be cleared when new booking is created\r\n            window.location.href = paymentUrl;\r\n          }\r\n        } else if (response?.status === 201) {\r\n          reservationId = response?.data?.reservation?._id;\r\n          saveReservationIdToBookingStack(reservationId);\r\n          const responseCheckout = await Factories.checkout_booking(\r\n            reservationId\r\n          );\r\n          const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n          if (paymentUrl) {\r\n            // Don't clear promotion here - user might come back from payment\r\n            // Promotion will be cleared when new booking is created\r\n            window.location.href = paymentUrl;\r\n          }\r\n        } else {\r\n          showToast.error(\"error create booking\");\r\n        }\r\n      } catch (error) {\r\n        \r\n        console.error(\"Error create payment: \", error.response?.data?.message);\r\n        setPaymentErrorMessage({\r\n          title: \"Payment Error\",\r\n          mainMessage: \"Unable to process your payment at this time\",\r\n          subMessage: error.response?.data?.message || \"Please try again later\"\r\n        });\r\n        setShowPaymentErrorModal(true);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error checking hotel status:\", error);\r\n      setShowModalStatusBooking(true);\r\n    }\r\n  };\r\n\r\n  const handleAccept = async () => {\r\n    const totalRoomPrice = selectedRooms.reduce(\r\n      (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n      0\r\n    );\r\n\r\n    if (totalRoomPrice > 0) {\r\n      // Final validation before creating booking\r\n      await createBooking();\r\n\r\n      // Only clear selection if booking was successful\r\n      // (createBooking will handle errors and not reach this point if failed)\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: [],\r\n          selectedServices: [],\r\n          hotelDetail: hotelDetail,\r\n        },\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleConfirmBooking = () => {\r\n    setShowAcceptModal(true);\r\n  };\r\n\r\n  // Only show loading spinner during initial load, not during re-renders\r\n  if (isInitialLoading || (!hotelDetail && !dataRestored)) {\r\n    return (\r\n      <div\r\n        className=\"d-flex justify-content-center align-items-center\"\r\n        style={{ height: \"100vh\" }}\r\n      >\r\n        <div className=\"spinner-border text-primary\" role=\"status\">\r\n          <span className=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // If data is restored but hotelDetail is still missing, redirect back\r\n  if (!hotelDetail && dataRestored) {\r\n    navigate(-1);\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"d-flex flex-column min-vh-100\"\r\n      style={{\r\n        backgroundImage: `url(${Banner})`,\r\n        backgroundSize: \"cover\",\r\n        backgroundPosition: \"center\",\r\n      }}\r\n    >\r\n      <Header />\r\n      <div\r\n        className=\"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\"\r\n        style={{ paddingTop: \"65px\", paddingBottom: \"50px\" }}\r\n      >\r\n        <Container className=\"mt-4\">\r\n          <Row className=\"justify-content-center\">\r\n            <ToastProvider />\r\n            {/* Left Card - Booking Details */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"booking-card text-white\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  marginBottom: \"20px\",\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"stars mb-2\"\r\n                  style={{\r\n                    justifyContent: \"flex-start\",\r\n                    justifyItems: \"self-start\",\r\n                  }}\r\n                >\r\n                  <StarRating rating={hotelDetail.star} />\r\n                </div>\r\n\r\n                <h4 className=\"hotel-name mb-1\">\r\n                  {hotelDetail.hotelName ?? \"\"}\r\n                </h4>\r\n\r\n                <p className=\"hotel-address small mb-4\">\r\n                  {hotelDetail.address ?? \"\"}\r\n                </p>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <h5 className=\"mb-4\">Your booking detail</h5>\r\n\r\n                <Row className=\"mb-4\">\r\n                  <Col xs={6}>\r\n                    <div className=\"checkin\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkin\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkinDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                  <Col xs={6}>\r\n                    <div className=\"checkout\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkout\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkoutDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                </Row>\r\n\r\n                <div className=\"stay-info mb-2\">\r\n                  <div className=\"d-flex justify-content-between mb-2\">\r\n                    <span>Total length of stay:</span>\r\n                    <span className=\"fw-bold\">{numberOfDays} night</span>{\" \"}\r\n                  </div>\r\n                  <div className=\"d-flex justify-content-between mb-3\">\r\n                    <span>Total number of people:</span>\r\n                    <span className=\"fw-bold\">\r\n                      {searchInfo.adults} Adults - {searchInfo.childrens}{\" \"}\r\n                      Childrens\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"selected-room mb-2\">\r\n                  <h5 className=\"mb-4\">You selected</h5>\r\n\r\n                  {selectedRooms.map(({ room, amount }) => (\r\n                    <div\r\n                      key={room._id}\r\n                      className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                    >\r\n                      <span>\r\n                        {amount} x {room.name} ({numberOfDays} days):\r\n                      </span>\r\n                      <span className=\"fw-bold\">\r\n                        {Utils.formatCurrency(\r\n                          room.price * amount * numberOfDays\r\n                        )}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n\r\n                  <div className=\"small mb-3\">\r\n                    <a\r\n                      className=\"text-blue text-decoration-none\"\r\n                      style={{ cursor: \"pointer\" }}\r\n                      onClick={handleBackToHomeDetail}\r\n                    >\r\n                      Change your selection\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Selected Services Section */}\r\n                {selectedServices.length > 0 && (\r\n                  <div className=\"selected-services mb-2\">\r\n                    <h5 className=\"mb-3\">Selected Services</h5>\r\n\r\n                    {selectedServices.map((service) => {\r\n                      const selectedDates = service.selectedDates || [];\r\n                      const serviceQuantity =\r\n                        service.quantity * selectedDates.length;\r\n                      const serviceTotal = service.price * serviceQuantity;\r\n\r\n                      return (\r\n                        <div\r\n                          key={service._id}\r\n                          className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                        >\r\n                          <span>\r\n                            {service.quantity} x {service.name} (\r\n                            {selectedDates.length} days):\r\n                          </span>\r\n                          <span className=\"fw-bold\">\r\n                            {Utils.formatCurrency(serviceTotal)}\r\n                          </span>\r\n                        </div>\r\n                      );\r\n                    })}\r\n\r\n                    <div className=\"small mb-3\">\r\n                      <a\r\n                        className=\"text-blue text-decoration-none\"\r\n                        style={{ cursor: \"pointer\" }}\r\n                        onClick={() => {\r\n                          dispatch({\r\n                            type: SearchActions.SAVE_SELECTED_ROOMS,\r\n                            payload: {\r\n                              selectedRooms: selectedRooms,\r\n                              selectedServices: selectedServices,\r\n                              hotelDetail: hotelDetail,\r\n                            },\r\n                          });\r\n                          navigate(-1);\r\n                        }}\r\n                      >\r\n                        Change your selection\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"promotion-section mb-3\">\r\n                  {/* Current applied promotion display */}\r\n                  {promotionDiscount > 0 ? (\r\n                    <Card\r\n                      className=\"promotion-applied mb-3\"\r\n                      style={{\r\n                        backgroundColor: \"rgba(40, 167, 69, 0.2)\",\r\n                        borderColor: \"#28a745\",\r\n                        border: \"2px solid #28a745\",\r\n                      }}\r\n                    >\r\n                      <Card.Body className=\"py-2\">\r\n                        <div className=\"d-flex justify-content-between align-items-center\">\r\n                          <div>\r\n                            <div className=\"d-flex align-items-center\">\r\n                              <FaTag className=\"text-success me-2\" />\r\n                              <span className=\"fw-bold text-success\">\r\n                                {promotionCode}\r\n                              </span>\r\n                            </div>\r\n                            <small className=\"text-success\">\r\n                              Save {Utils.formatCurrency(promotionDiscount)}\r\n                            </small>\r\n                          </div>\r\n                          <Button\r\n                            variant=\"outline-danger\"\r\n                            size=\"sm\"\r\n                            onClick={() =>\r\n                              handleApplyPromotionFromModal({\r\n                                code: \"\",\r\n                                discount: 0,\r\n                                message: \"\",\r\n                                promotionId: null,\r\n                              })\r\n                            }\r\n                            className=\"d-flex align-items-center\"\r\n                            disabled={\r\n                              isValidatingPromotion ||\r\n                              isValidatingPromotionBeforeBooking\r\n                            }\r\n                          >\r\n                            <FaTimes className=\"me-1\" />\r\n                            {isValidatingPromotion ||\r\n                            isValidatingPromotionBeforeBooking\r\n                              ? \"...\"\r\n                              : \"Remove\"}\r\n                          </Button>\r\n                        </div>\r\n                      </Card.Body>\r\n                    </Card>\r\n                  ) : (\r\n                    <div\r\n                      className=\"text-center py-3 mb-3\"\r\n                      style={{\r\n                        border: \"2px dashed rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"8px\",\r\n                        backgroundColor: \"rgba(255,255,255,0.05)\",\r\n                      }}\r\n                    >\r\n                      <FaTag className=\"text-muted mb-2\" size={24} />\r\n                      <div className=\"text-muted small\">\r\n                        No promotion applied\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Button to open promotion modal */}\r\n                  <Button\r\n                    variant=\"outline-light\"\r\n                    className=\"w-100 d-flex align-items-center justify-content-center\"\r\n                    onClick={() => setShowPromotionModal(true)}\r\n                    style={{\r\n                      borderStyle: \"dashed\",\r\n                      borderWidth: \"2px\",\r\n                      padding: \"12px\",\r\n                    }}\r\n                    disabled={\r\n                      isValidatingPromotion ||\r\n                      isValidatingPromotionBeforeBooking\r\n                    }\r\n                  >\r\n                    <FaTag className=\"me-2\" />\r\n                    {isValidatingPromotion || isValidatingPromotionBeforeBooking\r\n                      ? \"Validating...\"\r\n                      : promotionDiscount > 0\r\n                      ? \"Change Promotion\"\r\n                      : \"Select Promotion\"}\r\n                  </Button>\r\n\r\n                  {/* Validation status indicator */}\r\n                  {(isValidatingPromotion ||\r\n                    isValidatingPromotionBeforeBooking) && (\r\n                    <div className=\"text-center mt-2\">\r\n                      <small className=\"text-info\">\r\n                        <div\r\n                          className=\"spinner-border spinner-border-sm me-1\"\r\n                          role=\"status\"\r\n                        >\r\n                          <span className=\"visually-hidden\">Loading...</span>\r\n                        </div>\r\n                        Checking promotion validity...\r\n                      </small>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Price breakdown section */}\r\n                <div className=\"price-breakdown\">\r\n                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                    <span>Subtotal:</span>\r\n                    <span className=\"fw-bold\">\r\n                      {Utils.formatCurrency(subtotal)}\r\n                    </span>\r\n                  </div>\r\n\r\n                  {promotionDiscount > 0 && (\r\n                    <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                      <span className=\"text-success\">Discount:</span>\r\n                      <span className=\"fw-bold text-success\">\r\n                        -{Utils.formatCurrency(promotionDiscount)}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div\r\n                    className=\"booking-divider mb-2\"\r\n                    style={{\r\n                      height: \"1px\",\r\n                      backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                      margin: \"10px 0\",\r\n                    }}\r\n                  ></div>\r\n\r\n                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                    <h5 className=\"text-danger mb-0\">\r\n                      Total: {Utils.formatCurrency(finalPrice)}\r\n                    </h5>\r\n                  </div>\r\n                  <div className=\"small\">Includes taxes and fees</div>\r\n                </div>\r\n              </Card>\r\n            </Col>\r\n\r\n            {/* Right Card - Customer Information */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"info-card\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  color: \"white\",\r\n                }}\r\n              >\r\n                <h4 className=\"mb-4\">Check your information</h4>\r\n\r\n                <Form>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Full name</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={Auth.name}\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Email</Form.Label>\r\n                    <Form.Control\r\n                      type=\"email\"\r\n                      value={Auth.email}\r\n                      placeholder=\"<EMAIL>\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Phone</Form.Label>\r\n                    <Form.Control\r\n                      type=\"tel\"\r\n                      value={Auth.phoneNumber}\r\n                      placeholder=\"0912345678\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Who are you booking for?</Form.Label>\r\n                    <div>\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"mainGuest\"\r\n                        label=\"I'm the main guest\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"mainGuest\"}\r\n                        onChange={() => setBookingFor(\"mainGuest\")}\r\n                        className=\"mb-2\"\r\n                      />\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"someoneElse\"\r\n                        label=\"I'm booking for someone else\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"someoneElse\"}\r\n                        onChange={() => setBookingFor(\"someoneElse\")}\r\n                      />\r\n                    </div>\r\n                  </Form.Group>\r\n\r\n                  <div className=\"text-center\">\r\n                    <Button\r\n                      className=\"px-4 py-2\"\r\n                      style={{\r\n                        borderRadius: \"10px\",\r\n                        backgroundColor: \"white\",\r\n                        color: \"#007bff\",\r\n                        border: \"none\",\r\n                        fontWeight: \"bold\",\r\n                      }}\r\n                      onClick={handleConfirmBooking}\r\n                      disabled={\r\n                        isCheckingHotelStatus ||\r\n                        isValidatingPromotion ||\r\n                        isValidatingPromotionBeforeBooking\r\n                      }\r\n                    >\r\n                      {isValidatingPromotionBeforeBooking\r\n                        ? \"Validating Promotion...\"\r\n                        : isCheckingHotelStatus\r\n                        ? \"Checking Hotel...\"\r\n                        : \"Booking\"}\r\n                    </Button>\r\n                    {/* Accept Confirmation Modal */}\r\n                    <ConfirmationModal\r\n                      show={showAcceptModal}\r\n                      onHide={() => setShowAcceptModal(false)}\r\n                      onConfirm={handleAccept}\r\n                      title=\"Confirm Acceptance\"\r\n                      message=\"Do you want to proceed with this booking confirmation?\"\r\n                      confirmButtonText=\"Accept\"\r\n                      type=\"accept\"\r\n                    />\r\n                  </div>\r\n                </Form>\r\n              </Card>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n        <div>\r\n          <ChatBox />\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n\r\n      {/* Promotion Modal */}\r\n      <PromotionModal\r\n        show={showPromotionModal}\r\n        onHide={() => setShowPromotionModal(false)}\r\n        totalPrice={subtotal}\r\n        onApplyPromotion={handleApplyPromotionFromModal}\r\n        currentPromotionId={promotionId}\r\n      />\r\n\r\n      <HotelClosedModal\r\n        show={showModalStatusBooking}\r\n        onClose={() => {\r\n          setShowModalStatusBooking(false);\r\n        }}\r\n      />\r\n\r\n      {/* Promotion Error Modal */}\r\n      <PromotionErrorModal\r\n        show={showPromotionErrorModal}\r\n        onClose={() => {\r\n          setShowPromotionErrorModal(false);\r\n          setPromotionErrorMessage(\"\");\r\n          setInvalidPromotionCode(\"\");\r\n        }}\r\n        onSelectNewPromotion={() => {\r\n          setShowPromotionErrorModal(false);\r\n          setPromotionErrorMessage(\"\");\r\n          setInvalidPromotionCode(\"\");\r\n          setShowPromotionModal(true);\r\n        }}\r\n        errorMessage={promotionErrorMessage}\r\n        promotionCode={invalidPromotionCode}\r\n      />\r\n\r\n      <RoomClosedModal \r\n        show={showPaymentErrorModal}\r\n        onClose={() => setShowPaymentErrorModal(false)}\r\n        title={paymentErrorMessage.title}\r\n        mainMessage={paymentErrorMessage.mainMessage}\r\n        subMessage={paymentErrorMessage.subMessage}\r\n        buttonText=\"Try Again\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BookingCheckPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,OAAO;AACvD,SACEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,UAAU,QACL,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,QAAQ,gBAAgB;AAClE,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,SAASC,cAAc,EAAEC,cAAc,QAAQ,sBAAsB;AACrE,SAASC,cAAc,QAAQ,kCAAkC;AACjE,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,SAAS,MAAM,iCAAiC;AACvD,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,SAASC,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AACrE,OAAOC,gBAAgB,MAAM,kBAAkB;AAC/C,OAAOC,eAAe,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA;EAC7B,MAAMC,YAAY,GAAGR,gBAAgB,CAAC,CAAC,CAAC,CAAC;;EAEzC,MAAM,CAACS,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAE7C,QAAQ,CAAC,IAAI,CAAC;EACvC,MAAM8C,IAAI,GAAGxB,cAAc,CAAEyB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAACA,IAAI,CAAC;EACvD,MAAME,iBAAiB,GAAG1B,cAAc,CACrCyB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACD,iBAC1B,CAAC;EAED,MAAME,kBAAkB,GAAG5B,cAAc,CACtCyB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACE,aAC1B,CAAC;EACD,MAAMC,yBAAyB,GAAG9B,cAAc,CAC7CyB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACI,gBAC1B,CAAC;EACD,MAAMC,oBAAoB,GAAGhC,cAAc,CACxCyB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACM,WAC1B,CAAC;;EAED;EACA,MAAM;IACJC,YAAY,EAAEC,qBAAqB;IACnCC,UAAU,EAAEC,mBAAmB;IAC/BC;EACF,CAAC,GAAGtC,cAAc,CAACyB,KAAK,IAAIA,KAAK,CAACc,SAAS,CAAC;EAE5C,MAAMC,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM6C,QAAQ,GAAGxC,cAAc,CAAC,CAAC;EACjC,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,WAAW,CAAC;;EAEzD;EACA,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAG3E,QAAQ,CAAC;IAC7CmD,aAAa,EAAED,kBAAkB,IAAI,EAAE;IACvCG,gBAAgB,EAAED,yBAAyB,IAAI,EAAE;IACjDG,WAAW,EAAED,oBAAoB,IAAI,IAAI;IACzCsB,UAAU,EAAE5B;EACd,CAAC,CAAC;EAEF,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+E,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACiF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CACJmF,kCAAkC,EAClCC,qCAAqC,CACtC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACnB,MAAM,CAACqF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMsF,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,cAAc,GAAGN,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC;MAC5DjB,cAAc,CAACkB,cAAc,CAAC;;MAE9B;MACA9B,QAAQ,CAAC;QACP+B,IAAI,EAAElE,aAAa,CAACmE,mBAAmB;QACvCC,OAAO,EAAE;UACP7C,aAAa,EAAE0C,cAAc,CAAC1C,aAAa;UAC3CE,gBAAgB,EAAEwC,cAAc,CAACxC,gBAAgB;UACjDE,WAAW,EAAEsC,cAAc,CAACtC;QAC9B;MACF,CAAC,CAAC;IACJ;IACAuB,eAAe,CAAC,IAAI,CAAC;IACrBQ,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC,EAAE,CAACvB,QAAQ,CAAC,CAAC;;EAEd;EACA9D,SAAS,CAAC,MAAM;IACd,IAAI4E,YAAY,EAAE;MAChB,MAAMoB,KAAK,GAAGT,IAAI,CAACC,KAAK,CACtBC,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,MAC7C,CAAC;MACD,IAAIM,KAAK,EAAE;QAAA,IAAAC,qBAAA,EAAAC,qBAAA;QACT;QACA,MAAMC,cAAc,IAAAF,qBAAA,GAAGxB,WAAW,CAACnB,WAAW,cAAA2C,qBAAA,uBAAvBA,qBAAA,CAAyBG,GAAG;QACnD,MAAMC,YAAY,GAAGL,KAAK,CAACM,OAAO;QAClC,MAAMC,gBAAgB,GAAGhB,IAAI,CAACiB,SAAS,EAAAN,qBAAA,GACrCzB,WAAW,CAACvB,aAAa,cAAAgD,qBAAA,uBAAzBA,qBAAA,CACIO,GAAG,CAAEC,CAAC,KAAM;UAAEC,MAAM,EAAED,CAAC,CAACE,IAAI,CAACR,GAAG;UAAES,MAAM,EAAEH,CAAC,CAACG;QAAO,CAAC,CAAC,CAAC,CACvDC,IAAI,CAAC,CACV,CAAC;QACD,MAAMC,cAAc,GAAGf,KAAK,CAACgB,SAAS;QAEtC,IACEb,cAAc,KAAKE,YAAY,IAC/BE,gBAAgB,KAAKQ,cAAc,EACnC;UACA;UACAtB,cAAc,CAACwB,UAAU,CAAC,eAAe,CAAC;UAC1CC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;UAC7D;QACF;;QAEA;QACA,MAAMC,SAAS,GAAGpB,KAAK,CAACoB,SAAS,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC;QAC/C,MAAMC,QAAQ,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;QACvC,MAAMI,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;QAEjC,IAAID,QAAQ,GAAGC,WAAW,EAAE;UAC1B;UACAN,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UACnDjD,gBAAgB,CAAC8B,KAAK,CAAC/B,aAAa,IAAI,EAAE,CAAC;UAC3CG,oBAAoB,CAAC4B,KAAK,CAAC7B,iBAAiB,IAAI,CAAC,CAAC;UAClDG,mBAAmB,CAAC,yBAAyB,CAAC;UAC9CE,cAAc,CAACwB,KAAK,CAACzB,WAAW,IAAI,IAAI,CAAC;QAC3C,CAAC,MAAM;UACLL,gBAAgB,CAAC8B,KAAK,CAAC/B,aAAa,IAAI,EAAE,CAAC;UAC3CG,oBAAoB,CAAC4B,KAAK,CAAC7B,iBAAiB,IAAI,CAAC,CAAC;UAClDG,mBAAmB,CAAC0B,KAAK,CAAC3B,gBAAgB,IAAI,EAAE,CAAC;UACjDG,cAAc,CAACwB,KAAK,CAACzB,WAAW,IAAI,IAAI,CAAC;UACzC2C,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzCnB,KAAK,CAAC/B,aACR,CAAC;QACH;MACF;IACF;EACF,CAAC,EAAE,CAACW,YAAY,EAAEH,WAAW,CAACnB,WAAW,EAAEmB,WAAW,CAACvB,aAAa,CAAC,CAAC;;EAEtE;EACAlD,SAAS,CAAC,MAAM;IACd,IAAI4E,YAAY,EAAE;MAAA,IAAA6C,sBAAA,EAAAC,sBAAA;MAChB;MACAjC,cAAc,CAACkC,OAAO,CACpB,eAAe,EACfpC,IAAI,CAACiB,SAAS,CAAC;QACbvC,aAAa;QACbE,iBAAiB;QACjBE,gBAAgB;QAChBE,WAAW;QACX6C,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QAAE;QACvB;QACAhB,OAAO,GAAAmB,sBAAA,GAAEhD,WAAW,CAACnB,WAAW,cAAAmE,sBAAA,uBAAvBA,sBAAA,CAAyBrB,GAAG;QACrCY,SAAS,EAAEzB,IAAI,CAACiB,SAAS,EAAAkB,sBAAA,GACvBjD,WAAW,CAACvB,aAAa,cAAAwE,sBAAA,uBAAzBA,sBAAA,CACIjB,GAAG,CAAEC,CAAC,KAAM;UAAEC,MAAM,EAAED,CAAC,CAACE,IAAI,CAACR,GAAG;UAAES,MAAM,EAAEH,CAAC,CAACG;QAAO,CAAC,CAAC,CAAC,CACvDC,IAAI,CAAC,CACV;MACF,CAAC,CACH,CAAC;IACH;EACF,CAAC,EAAE,CACD7C,aAAa,EACbE,iBAAiB,EACjBE,gBAAgB,EAChBE,WAAW,EACXK,YAAY,EACZH,WAAW,CAACnB,WAAW,EACvBmB,WAAW,CAACvB,aAAa,CAC1B,CAAC;;EAEF;EACA,MAAMA,aAAa,GAAGuB,WAAW,CAACvB,aAAa;EAC/C,MAAME,gBAAgB,GAAGqB,WAAW,CAACrB,gBAAgB;EACrD,MAAME,WAAW,GAAGmB,WAAW,CAACnB,WAAW;EAC3C,MAAMqB,UAAU,GAAGF,WAAW,CAACE,UAAU;;EAEzC;EACA,MAAMiD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAG,IAAIR,IAAI,CAAC1C,UAAU,CAACmD,WAAW,CAAC;IAChD,MAAMC,QAAQ,GAAG,IAAIV,IAAI,CAAC1C,UAAU,CAACqD,YAAY,CAAC;IAClD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAGF,OAAO,CAAC;IAC7C,MAAMO,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOG,QAAQ;EACjB,CAAC;EAED,MAAME,YAAY,GAAGV,qBAAqB,CAAC,CAAC;;EAE5C;EACA,MAAMW,cAAc,GAAGrF,aAAa,CAACsF,MAAM,CACzC,CAACC,KAAK,EAAE;IAAE7B,IAAI;IAAEC;EAAO,CAAC,KAAK4B,KAAK,GAAG7B,IAAI,CAAC8B,KAAK,GAAG7B,MAAM,GAAGyB,YAAY,EACvE,CACF,CAAC;EACD,MAAMK,iBAAiB,GAAGvF,gBAAgB,CAACoF,MAAM,CAAC,CAACC,KAAK,EAAEG,OAAO,KAAK;IACpE,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;IACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAClD,MAAM;IAC/D,OAAO8C,KAAK,GAAGG,OAAO,CAACF,KAAK,GAAGI,eAAe;EAChD,CAAC,EAAE,CAAC,CAAC;EACL,MAAME,QAAQ,GAAGT,cAAc,GAAGI,iBAAiB;EACnD,MAAMM,UAAU,GAAGf,IAAI,CAACgB,GAAG,CAACF,QAAQ,GAAG7E,iBAAiB,EAAE,CAAC,CAAC;;EAE5D;EACAnE,SAAS,CAAC,MAAM;IACd,IACE,CAAC4E,YAAY,IACb,CAACX,aAAa,IACd,CAACM,WAAW,IACZJ,iBAAiB,KAAK,CAAC,EAEvB;;IAEF;IACA,MAAMgF,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjC;MACA,MAAMC,gBAAgB,GAAGD,UAAU,CAAC,MAAM;QACxCrE,wBAAwB,CAAC,IAAI,CAAC;MAChC,CAAC,EAAE,GAAG,CAAC;MAEPjB,QAAQ,CAACvC,cAAc,CAAC;QACtB+H,IAAI,EAAErF,aAAa;QACnBsF,WAAW,EAAEP,QAAQ;QACrBQ,SAAS,EAAGC,IAAI,IAAK;UACnBC,YAAY,CAACL,gBAAgB,CAAC;UAC9BtE,wBAAwB,CAAC,KAAK,CAAC;UAE/B,IAAI,CAAC0E,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACG,QAAQ,KAAKzF,iBAAiB,EAAE;YACtD;YACAiF,UAAU,CAAC,MAAM;cACflF,gBAAgB,CAAC,EAAE,CAAC;cACpBE,oBAAoB,CAAC,CAAC,CAAC;cACvBE,mBAAmB,CACjB,qDACF,CAAC;cACDE,cAAc,CAAC,IAAI,CAAC;cACpBiB,cAAc,CAACwB,UAAU,CAAC,eAAe,CAAC;YAC5C,CAAC,EAAE,CAAC,CAAC;UACP;QACF,CAAC;QACD4C,QAAQ,EAAGlH,KAAK,IAAK;UACnB+G,YAAY,CAACL,gBAAgB,CAAC;UAC9BtE,wBAAwB,CAAC,KAAK,CAAC;UAC/B;UACAqE,UAAU,CAAC,MAAM;YACflF,gBAAgB,CAAC,EAAE,CAAC;YACpBE,oBAAoB,CAAC,CAAC,CAAC;YACvBE,mBAAmB,CAAC,8BAA8B,CAAC;YACnDE,cAAc,CAAC,IAAI,CAAC;YACpBiB,cAAc,CAACwB,UAAU,CAAC,eAAe,CAAC;UAC5C,CAAC,EAAE,CAAC,CAAC;QACP;MACF,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMyC,YAAY,CAACP,SAAS,CAAC;EACtC,CAAC,EAAE,CAACvE,YAAY,EAAEoE,QAAQ,EAAE/E,aAAa,EAAEM,WAAW,EAAEJ,iBAAiB,EAAEL,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEvF;EACA,MAAMgG,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMxE,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B;MACAL,YAAY,CAACyE,GAAG,CAAC,CAAC;MAClBtE,cAAc,CAACkC,OAAO,CAAC,cAAc,EAAEpC,IAAI,CAACiB,SAAS,CAAClB,YAAY,CAAC,CAAC;IACtE;IACA;IACA;IACAzB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMmG,UAAU,GAAGA,CAAC;IAAEC;EAAO,CAAC,KAAK;IACjC,oBACE9H,OAAA;MAAK+H,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC3D,GAAG,CAAC,CAAC4D,CAAC,EAAEC,KAAK,KAC1BA,KAAK,GAAGL,MAAM,gBACZ9H,OAAA,CAAC1B,MAAM;QAAayJ,SAAS,EAAC;MAAa,GAA9BI,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA2B,CAAC,gBAE9CvI,OAAA,CAACzB,SAAS;QAAawJ,SAAS,EAAC;MAAM,GAAvBI,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAE7C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG7K,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8K,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/K,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACgL,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGjL,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACkL,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnL,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACoL,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrL,QAAQ,CAAC,EAAE,CAAC;;EAEpE;EACA,MAAM,CAACsL,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvL,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACwL,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzL,QAAQ,CAAC;IAC7D0L,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAMC,6BAA6B,GAAIC,aAAa,IAAK;IACvD;IACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;MAClC5H,gBAAgB,CAAC2H,aAAa,CAACvC,IAAI,CAAC;MACpClF,oBAAoB,CAACyH,aAAa,CAACjC,QAAQ,CAAC;MAC5CtF,mBAAmB,CAACuH,aAAa,CAACE,OAAO,CAAC;MAC1CvH,cAAc,CAACqH,aAAa,CAACtH,WAAW,CAAC;IAC3C,CAAC;;IAED;IACA6E,UAAU,CAAC0C,qBAAqB,EAAE,CAAC,CAAC;EACtC,CAAC;;EAED;EACA,MAAME,8BAA8B,GAAG,MAAAA,CAAA,KAAY;IACjD,IAAI,CAAC/H,aAAa,IAAI,CAACM,WAAW,IAAIJ,iBAAiB,KAAK,CAAC,EAAE;MAC7D,OAAO;QAAEwF,KAAK,EAAE;MAAK,CAAC,CAAC,CAAC;IAC1B;;IAEA;IACA,IAAIsC,iBAAiB,GAAG,KAAK;IAC7B,MAAM5C,gBAAgB,GAAGD,UAAU,CAAC,MAAM;MACxC6C,iBAAiB,GAAG,IAAI;MACxB9G,qCAAqC,CAAC,IAAI,CAAC;IAC7C,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,IAAI+G,OAAO,CAAEC,OAAO,IAAK;MAC9BrI,QAAQ,CAACvC,cAAc,CAAC;QACtB+H,IAAI,EAAErF,aAAa;QACnBsF,WAAW,EAAEP,QAAQ;QACrBQ,SAAS,EAAGC,IAAI,IAAK;UACnB;UACAC,YAAY,CAACL,gBAAgB,CAAC;UAC9B,IAAI4C,iBAAiB,EAAE;YACrB9G,qCAAqC,CAAC,KAAK,CAAC;UAC9C;UAEA,IAAI,CAACsE,IAAI,CAACE,KAAK,EAAE;YACfwC,OAAO,CAAC;cACNxC,KAAK,EAAE,KAAK;cACZoC,OAAO,EAAEtC,IAAI,CAACsC,OAAO,IAAI;YAC3B,CAAC,CAAC;YACF;UACF;UAEA,IAAItC,IAAI,CAACG,QAAQ,KAAKzF,iBAAiB,EAAE;YACvCgI,OAAO,CAAC;cACNxC,KAAK,EAAE,KAAK;cACZoC,OAAO,EACL;YACJ,CAAC,CAAC;YACF;UACF;UAEAI,OAAO,CAAC;YAAExC,KAAK,EAAE;UAAK,CAAC,CAAC;QAC1B,CAAC;QACDE,QAAQ,EAAGlH,KAAK,IAAK;UACnB;UACA+G,YAAY,CAACL,gBAAgB,CAAC;UAC9B,IAAI4C,iBAAiB,EAAE;YACrB9G,qCAAqC,CAAC,KAAK,CAAC;UAC9C;UACAgH,OAAO,CAAC;YACNxC,KAAK,EAAE,KAAK;YACZoC,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMK,6BAA6B,GAAG,MAAAA,CAAA,KAAY;IAChD,OAAO,IAAIF,OAAO,CAAC,CAACC,OAAO,EAAEE,MAAM,KAAK;MACtC;MACA,IAAIJ,iBAAiB,GAAG,KAAK;MAC7B,MAAM5C,gBAAgB,GAAGD,UAAU,CAAC,MAAM;QACxC6C,iBAAiB,GAAG,IAAI;QACxBhH,wBAAwB,CAAC,IAAI,CAAC;MAChC,CAAC,EAAE,GAAG,CAAC;MAEPnB,QAAQ,CAAC;QACP+B,IAAI,EAAEjE,YAAY,CAAC0K,kBAAkB;QACrCvG,OAAO,EAAE;UACPO,OAAO,EAAEhD,WAAW,CAAC8C,GAAG;UACxBmG,MAAM,EAAE1J,IAAI,CAACuD,GAAG;UAChBoD,SAAS,EAAGgD,KAAK,IAAK;YACpB9C,YAAY,CAACL,gBAAgB,CAAC;YAC9B,IAAI4C,iBAAiB,EAAE;cACrBhH,wBAAwB,CAAC,KAAK,CAAC;YACjC;YACA,IAAIuH,KAAK,CAACC,WAAW,KAAK,QAAQ,EAAE;cAClCN,OAAO,CAACK,KAAK,CAAC;YAChB,CAAC,MAAM;cACLH,MAAM,CAAC,IAAIK,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAClD;UACF,CAAC;UACD7C,QAAQ,EAAGlH,KAAK,IAAK;YACnB+G,YAAY,CAACL,gBAAgB,CAAC;YAC9B,IAAI4C,iBAAiB,EAAE;cACrBhH,wBAAwB,CAAC,KAAK,CAAC;YACjC;YACAoH,MAAM,CAAC,IAAIK,KAAK,CAAC/J,KAAK,IAAI,8BAA8B,CAAC,CAAC;UAC5D,CAAC;UACDgK,OAAO,EAAEA,CAAA,KAAM;YACbjD,YAAY,CAACL,gBAAgB,CAAC;YAC9B,IAAI4C,iBAAiB,EAAE;cACrBhH,wBAAwB,CAAC,KAAK,CAAC;YACjC;YACAoH,MAAM,CAAC,IAAIK,KAAK,CAAC,0CAA0C,CAAC,CAAC;UAC/D;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF;MACA,MAAMC,mBAAmB,GAAG,MAAMb,8BAA8B,CAAC,CAAC;MAClE,IAAI,CAACa,mBAAmB,CAAClD,KAAK,EAAE;QAC9B;QACAuB,wBAAwB,CAAC2B,mBAAmB,CAACd,OAAO,CAAC;QACrDX,uBAAuB,CAACnH,aAAa,CAAC;;QAEtC;QACAC,gBAAgB,CAAC,EAAE,CAAC;QACpBE,oBAAoB,CAAC,CAAC,CAAC;QACvBE,mBAAmB,CAAC,EAAE,CAAC;QACvBE,cAAc,CAAC,IAAI,CAAC;QACpBiB,cAAc,CAACwB,UAAU,CAAC,eAAe,CAAC;;QAE1C;QACA+D,0BAA0B,CAAC,IAAI,CAAC;QAChC;MACF;;MAEA;MACA,MAAMwB,KAAK,GAAG,MAAMJ,6BAA6B,CAAC,CAAC;MACnDlF,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEqF,KAAK,CAAC;MACxD,MAAMjE,cAAc,GAAGrF,aAAa,CAACsF,MAAM,CACzC,CAACC,KAAK,EAAE;QAAE7B,IAAI;QAAEC;MAAO,CAAC,KAAK4B,KAAK,GAAG7B,IAAI,CAAC8B,KAAK,GAAG7B,MAAM,GAAGyB,YAAY,EACvE,CACF,CAAC;MAED,MAAMK,iBAAiB,GAAGvF,gBAAgB,CAACoF,MAAM,CAAC,CAACC,KAAK,EAAEG,OAAO,KAAK;QACpE,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;QACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAClD,MAAM;QAC/D,OAAO8C,KAAK,GAAGG,OAAO,CAACF,KAAK,GAAGI,eAAe;MAChD,CAAC,EAAE,CAAC,CAAC;MAEL,MAAMgE,eAAe,GAAGvE,cAAc,GAAGI,iBAAiB;MAE1D,MAAMoE,MAAM,GAAG;QACbzG,OAAO,EAAEhD,WAAW,CAAC8C,GAAG;QACxB4G,YAAY,EAAErI,UAAU,CAACqD,YAAY;QACrCiF,WAAW,EAAEtI,UAAU,CAACmD,WAAW;QACnCoF,UAAU,EAAEJ,eAAe;QAAE;QAC7B7D,UAAU,EAAEA,UAAU;QAAE;QACxBkE,WAAW,EAAEjK,aAAa,CAACuD,GAAG,CAAC,CAAC;UAAEG,IAAI;UAAEC;QAAO,CAAC,MAAM;UACpDD,IAAI,EAAE;YACJR,GAAG,EAAEQ,IAAI,CAACR;UACZ,CAAC;UACDS,MAAM,EAAEA;QACV,CAAC,CAAC,CAAC;QACHuG,cAAc,EAAEhK,gBAAgB,CAACqD,GAAG,CAAEmC,OAAO;UAAA,IAAAyE,qBAAA;UAAA,OAAM;YACjDjH,GAAG,EAAEwC,OAAO,CAACxC,GAAG;YAChB2C,QAAQ,EAAEH,OAAO,CAACG,QAAQ,IAAI,EAAAsE,qBAAA,GAAAzE,OAAO,CAACC,aAAa,cAAAwE,qBAAA,uBAArBA,qBAAA,CAAuB1H,MAAM,KAAI,CAAC,CAAC;YACjE2H,UAAU,EAAE1E,OAAO,CAACC,aAAa,IAAI;UACvC,CAAC;QAAA,CAAC,CAAC;QACH;QACA,IAAItE,WAAW,IAAI;UAAEA;QAAY,CAAC,CAAC;QACnC,IAAIJ,iBAAiB,GAAG,CAAC,IAAI;UAAEA;QAAkB,CAAC;MACpD,CAAC;MAED+C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE4F,MAAM,CAAC;;MAEjC;MACA,MAAMQ,+BAA+B,GAAIC,aAAa,IAAK;QACzD,IAAIA,aAAa,EAAE;UACjB,MAAMlI,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;UACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;YAC3BL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC6H,aAAa,GAAGA,aAAa;YACnE/H,cAAc,CAACkC,OAAO,CACpB,cAAc,EACdpC,IAAI,CAACiB,SAAS,CAAClB,YAAY,CAC7B,CAAC;UACH;QACF;MACF,CAAC;MACD,IAAI;QACF,IAAIkI,aAAa,GAAG,IAAI;QACxB,MAAMlI,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;QACD,IACEJ,YAAY,CAACK,MAAM,GAAG,CAAC,IACvBL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC6H,aAAa,EACnD;UACAA,aAAa,GAAGlI,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC6H,aAAa;QACrE;QACA,MAAMC,QAAQ,GAAG,MAAMhM,SAAS,CAACiM,cAAc,CAAC;UAC9C,GAAGX,MAAM;UACTS;QACF,CAAC,CAAC;QACFtG,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEsG,QAAQ,CAAC;QACrC,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;UAAA,IAAAC,cAAA,EAAAC,qBAAA,EAAAC,qBAAA;UAC5BN,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAG,cAAA,GAARH,QAAQ,CAAEhE,IAAI,cAAAmE,cAAA,wBAAAC,qBAAA,GAAdD,cAAA,CAAgBG,iBAAiB,cAAAF,qBAAA,uBAAjCA,qBAAA,CAAmCzH,GAAG;UACtDmH,+BAA+B,CAACC,aAAa,CAAC;UAC9C,MAAMQ,mBAAmB,GAAGR,aAAa;UACzC,MAAMS,gBAAgB,GAAG,MAAMxM,SAAS,CAACyM,gBAAgB,CACvDF,mBACF,CAAC;UACD9G,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8G,gBAAgB,CAAC;UACrD,MAAME,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAH,qBAAA,GAAhBG,gBAAgB,CAAExE,IAAI,cAAAqE,qBAAA,uBAAtBA,qBAAA,CAAwBM,UAAU;UACrD,IAAID,UAAU,EAAE;YACd;YACA;YACAE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;UACnC;QACF,CAAC,MAAM,IAAI,CAAAV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;UAAA,IAAAa,eAAA,EAAAC,qBAAA,EAAAC,sBAAA;UACnClB,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAe,eAAA,GAARf,QAAQ,CAAEhE,IAAI,cAAA+E,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBG,WAAW,cAAAF,qBAAA,uBAA3BA,qBAAA,CAA6BrI,GAAG;UAChDmH,+BAA+B,CAACC,aAAa,CAAC;UAC9C,MAAMS,gBAAgB,GAAG,MAAMxM,SAAS,CAACyM,gBAAgB,CACvDV,aACF,CAAC;UACD,MAAMW,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAS,sBAAA,GAAhBT,gBAAgB,CAAExE,IAAI,cAAAiF,sBAAA,uBAAtBA,sBAAA,CAAwBN,UAAU;UACrD,IAAID,UAAU,EAAE;YACd;YACA;YACAE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;UACnC;QACF,CAAC,MAAM;UACLrM,SAAS,CAACa,KAAK,CAAC,sBAAsB,CAAC;QACzC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QAAA,IAAAiM,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;QAEd7H,OAAO,CAACvE,KAAK,CAAC,wBAAwB,GAAAiM,eAAA,GAAEjM,KAAK,CAAC8K,QAAQ,cAAAmB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBnF,IAAI,cAAAoF,oBAAA,uBAApBA,oBAAA,CAAsB9C,OAAO,CAAC;QACtEP,sBAAsB,CAAC;UACrBC,KAAK,EAAE,eAAe;UACtBC,WAAW,EAAE,6CAA6C;UAC1DC,UAAU,EAAE,EAAAmD,gBAAA,GAAAnM,KAAK,CAAC8K,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrF,IAAI,cAAAsF,qBAAA,uBAApBA,qBAAA,CAAsBhD,OAAO,KAAI;QAC/C,CAAC,CAAC;QACFT,wBAAwB,CAAC,IAAI,CAAC;MAChC;IACF,CAAC,CAAC,OAAO3I,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDD,yBAAyB,CAAC,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAMsM,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMzG,cAAc,GAAGrF,aAAa,CAACsF,MAAM,CACzC,CAACC,KAAK,EAAE;MAAE7B,IAAI;MAAEC;IAAO,CAAC,KAAK4B,KAAK,GAAG7B,IAAI,CAAC8B,KAAK,GAAG7B,MAAM,GAAGyB,YAAY,EACvE,CACF,CAAC;IAED,IAAIC,cAAc,GAAG,CAAC,EAAE;MACtB;MACA,MAAMqE,aAAa,CAAC,CAAC;;MAErB;MACA;MACA9I,QAAQ,CAAC;QACP+B,IAAI,EAAElE,aAAa,CAACmE,mBAAmB;QACvCC,OAAO,EAAE;UACP7C,aAAa,EAAE,EAAE;UACjBE,gBAAgB,EAAE,EAAE;UACpBE,WAAW,EAAEA;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM2L,oBAAoB,GAAGA,CAAA,KAAM;IACjCrE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,IAAIxF,gBAAgB,IAAK,CAAC9B,WAAW,IAAI,CAACsB,YAAa,EAAE;IACvD,oBACEzC,OAAA;MACE+H,SAAS,EAAC,kDAAkD;MAC5DgF,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAhF,QAAA,eAE3BhI,OAAA;QAAK+H,SAAS,EAAC,6BAA6B;QAACkF,IAAI,EAAC,QAAQ;QAAAjF,QAAA,eACxDhI,OAAA;UAAM+H,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACpH,WAAW,IAAIsB,YAAY,EAAE;IAChCf,QAAQ,CAAC,CAAC,CAAC,CAAC;IACZ,OAAO,IAAI;EACb;EAEA,oBACE1B,OAAA;IACE+H,SAAS,EAAC,+BAA+B;IACzCgF,KAAK,EAAE;MACLG,eAAe,EAAE,OAAOxO,MAAM,GAAG;MACjCyO,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE;IACtB,CAAE;IAAApF,QAAA,gBAEFhI,OAAA,CAACrB,MAAM;MAAAyJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVvI,OAAA;MACE+H,SAAS,EAAC,8EAA8E;MACxFgF,KAAK,EAAE;QAAEM,UAAU,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAO,CAAE;MAAAtF,QAAA,gBAErDhI,OAAA,CAACjC,SAAS;QAACgK,SAAS,EAAC,MAAM;QAAAC,QAAA,eACzBhI,OAAA,CAAChC,GAAG;UAAC+J,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrChI,OAAA,CAACJ,aAAa;YAAAwI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEjBvI,OAAA,CAAC/B,GAAG;YAACsP,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAxF,QAAA,eAChBhI,OAAA,CAAC9B,IAAI;cACH6J,SAAS,EAAC,yBAAyB;cACnCgF,KAAK,EAAE;gBACLU,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfC,YAAY,EAAE;cAChB,CAAE;cAAA5F,QAAA,gBAEFhI,OAAA;gBACE+H,SAAS,EAAC,YAAY;gBACtBgF,KAAK,EAAE;kBACLc,cAAc,EAAE,YAAY;kBAC5BC,YAAY,EAAE;gBAChB,CAAE;gBAAA9F,QAAA,eAEFhI,OAAA,CAAC6H,UAAU;kBAACC,MAAM,EAAE3G,WAAW,CAAC4M;gBAAK;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAENvI,OAAA;gBAAI+H,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAA7H,qBAAA,GAC5BgB,WAAW,CAAC6M,SAAS,cAAA7N,qBAAA,cAAAA,qBAAA,GAAI;cAAE;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAELvI,OAAA;gBAAG+H,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GAAA5H,oBAAA,GACpCe,WAAW,CAAC8M,OAAO,cAAA7N,oBAAA,cAAAA,oBAAA,GAAI;cAAE;gBAAAgI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEJvI,OAAA;gBACE+H,SAAS,EAAC,sBAAsB;gBAChCgF,KAAK,EAAE;kBACLC,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPvI,OAAA;gBAAI+H,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAmB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE7CvI,OAAA,CAAChC,GAAG;gBAAC+J,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBhI,OAAA,CAAC/B,GAAG;kBAACkQ,EAAE,EAAE,CAAE;kBAAAnG,QAAA,eACThI,OAAA;oBAAK+H,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACtBhI,OAAA;sBACE+H,SAAS,EAAC,oBAAoB;sBAC9BgF,KAAK,EAAE;wBAAEqB,QAAQ,EAAE;sBAAG,CAAE;sBAAApG,QAAA,EACzB;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNvI,OAAA;sBAAK+H,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClB3I,KAAK,CAACgP,OAAO,CAAC7L,UAAU,CAACmD,WAAW,EAAE,CAAC;oBAAC;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvI,OAAA,CAAC/B,GAAG;kBAACkQ,EAAE,EAAE,CAAE;kBAAAnG,QAAA,eACThI,OAAA;oBAAK+H,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvBhI,OAAA;sBACE+H,SAAS,EAAC,oBAAoB;sBAC9BgF,KAAK,EAAE;wBAAEqB,QAAQ,EAAE;sBAAG,CAAE;sBAAApG,QAAA,EACzB;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNvI,OAAA;sBAAK+H,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClB3I,KAAK,CAACgP,OAAO,CAAC7L,UAAU,CAACqD,YAAY,EAAE,CAAC;oBAAC;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvI,OAAA;gBAAK+H,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BhI,OAAA;kBAAK+H,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDhI,OAAA;oBAAAgI,QAAA,EAAM;kBAAqB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClCvI,OAAA;oBAAM+H,SAAS,EAAC,SAAS;oBAAAC,QAAA,GAAE7B,YAAY,EAAC,QAAM;kBAAA;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAAC,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNvI,OAAA;kBAAK+H,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDhI,OAAA;oBAAAgI,QAAA,EAAM;kBAAuB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpCvI,OAAA;oBAAM+H,SAAS,EAAC,SAAS;oBAAAC,QAAA,GACtBxF,UAAU,CAAC8L,MAAM,EAAC,YAAU,EAAC9L,UAAU,CAAC+L,SAAS,EAAE,GAAG,EAAC,WAE1D;kBAAA;oBAAAnG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvI,OAAA;gBACE+H,SAAS,EAAC,sBAAsB;gBAChCgF,KAAK,EAAE;kBACLC,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPvI,OAAA;gBAAK+H,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjChI,OAAA;kBAAI+H,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAY;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAErCxH,aAAa,CAACuD,GAAG,CAAC,CAAC;kBAAEG,IAAI;kBAAEC;gBAAO,CAAC,kBAClC1E,OAAA;kBAEE+H,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBAElEhI,OAAA;oBAAAgI,QAAA,GACGtD,MAAM,EAAC,KAAG,EAACD,IAAI,CAAC+J,IAAI,EAAC,IAAE,EAACrI,YAAY,EAAC,SACxC;kBAAA;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPvI,OAAA;oBAAM+H,SAAS,EAAC,SAAS;oBAAAC,QAAA,EACtB3I,KAAK,CAACoP,cAAc,CACnBhK,IAAI,CAAC8B,KAAK,GAAG7B,MAAM,GAAGyB,YACxB;kBAAC;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAVF9D,IAAI,CAACR,GAAG;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACN,CAAC,eAEFvI,OAAA;kBAAK+H,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBhI,OAAA;oBACE+H,SAAS,EAAC,gCAAgC;oBAC1CgF,KAAK,EAAE;sBAAE2B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAEhH,sBAAuB;oBAAAK,QAAA,EACjC;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLtH,gBAAgB,CAACuC,MAAM,GAAG,CAAC,iBAC1BxD,OAAA;gBAAK+H,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrChI,OAAA;kBAAI+H,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAE1CtH,gBAAgB,CAACqD,GAAG,CAAEmC,OAAO,IAAK;kBACjC,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;kBACjD,MAAMC,eAAe,GACnBF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAClD,MAAM;kBACzC,MAAMoL,YAAY,GAAGnI,OAAO,CAACF,KAAK,GAAGI,eAAe;kBAEpD,oBACE3G,OAAA;oBAEE+H,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,gBAElEhI,OAAA;sBAAAgI,QAAA,GACGvB,OAAO,CAACG,QAAQ,EAAC,KAAG,EAACH,OAAO,CAAC+H,IAAI,EAAC,IACnC,EAAC9H,aAAa,CAAClD,MAAM,EAAC,SACxB;oBAAA;sBAAA4E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPvI,OAAA;sBAAM+H,SAAS,EAAC,SAAS;sBAAAC,QAAA,EACtB3I,KAAK,CAACoP,cAAc,CAACG,YAAY;oBAAC;sBAAAxG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA,GATF9B,OAAO,CAACxC,GAAG;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUb,CAAC;gBAEV,CAAC,CAAC,eAEFvI,OAAA;kBAAK+H,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBhI,OAAA;oBACE+H,SAAS,EAAC,gCAAgC;oBAC1CgF,KAAK,EAAE;sBAAE2B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAEA,CAAA,KAAM;sBACbhN,QAAQ,CAAC;wBACP+B,IAAI,EAAElE,aAAa,CAACmE,mBAAmB;wBACvCC,OAAO,EAAE;0BACP7C,aAAa,EAAEA,aAAa;0BAC5BE,gBAAgB,EAAEA,gBAAgB;0BAClCE,WAAW,EAAEA;wBACf;sBACF,CAAC,CAAC;sBACFO,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACd,CAAE;oBAAAsG,QAAA,EACH;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDvI,OAAA;gBACE+H,SAAS,EAAC,sBAAsB;gBAChCgF,KAAK,EAAE;kBACLC,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPvI,OAAA;gBAAK+H,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,GAEpChG,iBAAiB,GAAG,CAAC,gBACpBhC,OAAA,CAAC9B,IAAI;kBACH6J,SAAS,EAAC,wBAAwB;kBAClCgF,KAAK,EAAE;oBACLU,eAAe,EAAE,wBAAwB;oBACzCoB,WAAW,EAAE,SAAS;oBACtBC,MAAM,EAAE;kBACV,CAAE;kBAAA9G,QAAA,eAEFhI,OAAA,CAAC9B,IAAI,CAAC6Q,IAAI;oBAAChH,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACzBhI,OAAA;sBAAK+H,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChEhI,OAAA;wBAAAgI,QAAA,gBACEhI,OAAA;0BAAK+H,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,gBACxChI,OAAA,CAACxB,KAAK;4BAACuJ,SAAS,EAAC;0BAAmB;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACvCvI,OAAA;4BAAM+H,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EACnClG;0BAAa;4BAAAsG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACNvI,OAAA;0BAAO+H,SAAS,EAAC,cAAc;0BAAAC,QAAA,GAAC,OACzB,EAAC3I,KAAK,CAACoP,cAAc,CAACzM,iBAAiB,CAAC;wBAAA;0BAAAoG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNvI,OAAA,CAAC5B,MAAM;wBACL4Q,OAAO,EAAC,gBAAgB;wBACxBC,IAAI,EAAC,IAAI;wBACTN,OAAO,EAAEA,CAAA,KACPlF,6BAA6B,CAAC;0BAC5BtC,IAAI,EAAE,EAAE;0BACRM,QAAQ,EAAE,CAAC;0BACXmC,OAAO,EAAE,EAAE;0BACXxH,WAAW,EAAE;wBACf,CAAC,CACF;wBACD2F,SAAS,EAAC,2BAA2B;wBACrCmH,QAAQ,EACNvM,qBAAqB,IACrBI,kCACD;wBAAAiF,QAAA,gBAEDhI,OAAA,CAACvB,OAAO;0BAACsJ,SAAS,EAAC;wBAAM;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EAC3B5F,qBAAqB,IACtBI,kCAAkC,GAC9B,KAAK,GACL,QAAQ;sBAAA;wBAAAqF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,gBAEPvI,OAAA;kBACE+H,SAAS,EAAC,uBAAuB;kBACjCgF,KAAK,EAAE;oBACL+B,MAAM,EAAE,kCAAkC;oBAC1CpB,YAAY,EAAE,KAAK;oBACnBD,eAAe,EAAE;kBACnB,CAAE;kBAAAzF,QAAA,gBAEFhI,OAAA,CAACxB,KAAK;oBAACuJ,SAAS,EAAC,iBAAiB;oBAACkH,IAAI,EAAE;kBAAG;oBAAA7G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/CvI,OAAA;oBAAK+H,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAC;kBAElC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAGDvI,OAAA,CAAC5B,MAAM;kBACL4Q,OAAO,EAAC,eAAe;kBACvBjH,SAAS,EAAC,wDAAwD;kBAClE4G,OAAO,EAAEA,CAAA,KAAMhG,qBAAqB,CAAC,IAAI,CAAE;kBAC3CoE,KAAK,EAAE;oBACLoC,WAAW,EAAE,QAAQ;oBACrBC,WAAW,EAAE,KAAK;oBAClBzB,OAAO,EAAE;kBACX,CAAE;kBACFuB,QAAQ,EACNvM,qBAAqB,IACrBI,kCACD;kBAAAiF,QAAA,gBAEDhI,OAAA,CAACxB,KAAK;oBAACuJ,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACzB5F,qBAAqB,IAAII,kCAAkC,GACxD,eAAe,GACff,iBAAiB,GAAG,CAAC,GACrB,kBAAkB,GAClB,kBAAkB;gBAAA;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,EAGR,CAAC5F,qBAAqB,IACrBI,kCAAkC,kBAClC/C,OAAA;kBAAK+H,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/BhI,OAAA;oBAAO+H,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBAC1BhI,OAAA;sBACE+H,SAAS,EAAC,uCAAuC;sBACjDkF,IAAI,EAAC,QAAQ;sBAAAjF,QAAA,eAEbhI,OAAA;wBAAM+H,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,EAAC;sBAAU;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,kCAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNvI,OAAA;gBAAK+H,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BhI,OAAA;kBAAK+H,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrEhI,OAAA;oBAAAgI,QAAA,EAAM;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtBvI,OAAA;oBAAM+H,SAAS,EAAC,SAAS;oBAAAC,QAAA,EACtB3I,KAAK,CAACoP,cAAc,CAAC5H,QAAQ;kBAAC;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EAELvG,iBAAiB,GAAG,CAAC,iBACpBhC,OAAA;kBAAK+H,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrEhI,OAAA;oBAAM+H,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/CvI,OAAA;oBAAM+H,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,GAAC,GACpC,EAAC3I,KAAK,CAACoP,cAAc,CAACzM,iBAAiB,CAAC;kBAAA;oBAAAoG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACN,eAEDvI,OAAA;kBACE+H,SAAS,EAAC,sBAAsB;kBAChCgF,KAAK,EAAE;oBACLC,MAAM,EAAE,KAAK;oBACbS,eAAe,EAAE,uBAAuB;oBACxCS,MAAM,EAAE;kBACV;gBAAE;kBAAA9F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEPvI,OAAA;kBAAK+H,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAChEhI,OAAA;oBAAI+H,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAAC,SACxB,EAAC3I,KAAK,CAACoP,cAAc,CAAC3H,UAAU,CAAC;kBAAA;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNvI,OAAA;kBAAK+H,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNvI,OAAA,CAAC/B,GAAG;YAACsP,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAxF,QAAA,eAChBhI,OAAA,CAAC9B,IAAI;cACH6J,SAAS,EAAC,WAAW;cACrBgF,KAAK,EAAE;gBACLU,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACf0B,KAAK,EAAE;cACT,CAAE;cAAArH,QAAA,gBAEFhI,OAAA;gBAAI+H,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAsB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEhDvI,OAAA,CAAC7B,IAAI;gBAAA6J,QAAA,gBACHhI,OAAA,CAAC7B,IAAI,CAACmR,KAAK;kBAACvH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BhI,OAAA,CAAC7B,IAAI,CAACoR,KAAK;oBAAAvH,QAAA,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClCvI,OAAA,CAAC7B,IAAI,CAACqR,OAAO;oBACX9L,IAAI,EAAC,MAAM;oBACX+L,KAAK,EAAE/O,IAAI,CAAC8N,IAAK;oBACjBzG,SAAS,EAAC,2BAA2B;oBACrCgF,KAAK,EAAE;sBACL+B,MAAM,EAAE,iCAAiC;sBACzCpB,YAAY,EAAE;oBAChB;kBAAE;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbvI,OAAA,CAAC7B,IAAI,CAACmR,KAAK;kBAACvH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BhI,OAAA,CAAC7B,IAAI,CAACoR,KAAK;oBAAAvH,QAAA,EAAC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BvI,OAAA,CAAC7B,IAAI,CAACqR,OAAO;oBACX9L,IAAI,EAAC,OAAO;oBACZ+L,KAAK,EAAE/O,IAAI,CAACgP,KAAM;oBAClBC,WAAW,EAAC,sBAAsB;oBAClC5H,SAAS,EAAC,2BAA2B;oBACrCgF,KAAK,EAAE;sBACL+B,MAAM,EAAE,iCAAiC;sBACzCpB,YAAY,EAAE;oBAChB;kBAAE;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbvI,OAAA,CAAC7B,IAAI,CAACmR,KAAK;kBAACvH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BhI,OAAA,CAAC7B,IAAI,CAACoR,KAAK;oBAAAvH,QAAA,EAAC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BvI,OAAA,CAAC7B,IAAI,CAACqR,OAAO;oBACX9L,IAAI,EAAC,KAAK;oBACV+L,KAAK,EAAE/O,IAAI,CAACkP,WAAY;oBACxBD,WAAW,EAAC,YAAY;oBACxB5H,SAAS,EAAC,2BAA2B;oBACrCgF,KAAK,EAAE;sBACL+B,MAAM,EAAE,iCAAiC;sBACzCpB,YAAY,EAAE;oBAChB;kBAAE;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbvI,OAAA,CAAC7B,IAAI,CAACmR,KAAK;kBAACvH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BhI,OAAA,CAAC7B,IAAI,CAACoR,KAAK;oBAAAvH,QAAA,EAAC;kBAAwB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjDvI,OAAA;oBAAAgI,QAAA,gBACEhI,OAAA,CAAC7B,IAAI,CAAC0R,KAAK;sBACTnM,IAAI,EAAC,OAAO;sBACZoM,EAAE,EAAC,WAAW;sBACdC,KAAK,EAAC,oBAAoB;sBAC1BvB,IAAI,EAAC,YAAY;sBACjBwB,OAAO,EAAEpO,UAAU,KAAK,WAAY;sBACpCqO,QAAQ,EAAEA,CAAA,KAAMpO,aAAa,CAAC,WAAW,CAAE;sBAC3CkG,SAAS,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACFvI,OAAA,CAAC7B,IAAI,CAAC0R,KAAK;sBACTnM,IAAI,EAAC,OAAO;sBACZoM,EAAE,EAAC,aAAa;sBAChBC,KAAK,EAAC,8BAA8B;sBACpCvB,IAAI,EAAC,YAAY;sBACjBwB,OAAO,EAAEpO,UAAU,KAAK,aAAc;sBACtCqO,QAAQ,EAAEA,CAAA,KAAMpO,aAAa,CAAC,aAAa;oBAAE;sBAAAuG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbvI,OAAA;kBAAK+H,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhI,OAAA,CAAC5B,MAAM;oBACL2J,SAAS,EAAC,WAAW;oBACrBgF,KAAK,EAAE;sBACLW,YAAY,EAAE,MAAM;sBACpBD,eAAe,EAAE,OAAO;sBACxB4B,KAAK,EAAE,SAAS;sBAChBP,MAAM,EAAE,MAAM;sBACdoB,UAAU,EAAE;oBACd,CAAE;oBACFvB,OAAO,EAAE7B,oBAAqB;oBAC9BoC,QAAQ,EACNrM,qBAAqB,IACrBF,qBAAqB,IACrBI,kCACD;oBAAAiF,QAAA,EAEAjF,kCAAkC,GAC/B,yBAAyB,GACzBF,qBAAqB,GACrB,mBAAmB,GACnB;kBAAS;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eAETvI,OAAA,CAACjB,iBAAiB;oBAChBoR,IAAI,EAAE3H,eAAgB;oBACtB4H,MAAM,EAAEA,CAAA,KAAM3H,kBAAkB,CAAC,KAAK,CAAE;oBACxC4H,SAAS,EAAExD,YAAa;oBACxBvD,KAAK,EAAC,oBAAoB;oBAC1BM,OAAO,EAAC,wDAAwD;oBAChE0G,iBAAiB,EAAC,QAAQ;oBAC1B5M,IAAI,EAAC;kBAAQ;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACZvI,OAAA;QAAAgI,QAAA,eACEhI,OAAA,CAACT,OAAO;UAAA6I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNvI,OAAA,CAACpB,MAAM;MAAAwJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVvI,OAAA,CAAChB,cAAc;MACbmR,IAAI,EAAEzH,kBAAmB;MACzB0H,MAAM,EAAEA,CAAA,KAAMzH,qBAAqB,CAAC,KAAK,CAAE;MAC3CoC,UAAU,EAAElE,QAAS;MACrB0J,gBAAgB,EAAE9G,6BAA8B;MAChD+G,kBAAkB,EAAEpO;IAAY;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEFvI,OAAA,CAACN,gBAAgB;MACfyQ,IAAI,EAAE7P,sBAAuB;MAC7BmQ,OAAO,EAAEA,CAAA,KAAM;QACblQ,yBAAyB,CAAC,KAAK,CAAC;MAClC;IAAE;MAAA6H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFvI,OAAA,CAACf,mBAAmB;MAClBkR,IAAI,EAAEvH,uBAAwB;MAC9B6H,OAAO,EAAEA,CAAA,KAAM;QACb5H,0BAA0B,CAAC,KAAK,CAAC;QACjCE,wBAAwB,CAAC,EAAE,CAAC;QAC5BE,uBAAuB,CAAC,EAAE,CAAC;MAC7B,CAAE;MACFyH,oBAAoB,EAAEA,CAAA,KAAM;QAC1B7H,0BAA0B,CAAC,KAAK,CAAC;QACjCE,wBAAwB,CAAC,EAAE,CAAC;QAC5BE,uBAAuB,CAAC,EAAE,CAAC;QAC3BN,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAE;MACFgI,YAAY,EAAE7H,qBAAsB;MACpChH,aAAa,EAAEkH;IAAqB;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAEFvI,OAAA,CAACF,eAAe;MACdqQ,IAAI,EAAEjH,qBAAsB;MAC5BuH,OAAO,EAAEA,CAAA,KAAMtH,wBAAwB,CAAC,KAAK,CAAE;MAC/CG,KAAK,EAAEF,mBAAmB,CAACE,KAAM;MACjCC,WAAW,EAAEH,mBAAmB,CAACG,WAAY;MAC7CC,UAAU,EAAEJ,mBAAmB,CAACI,UAAW;MAC3CoH,UAAU,EAAC;IAAW;MAAAxI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACrI,EAAA,CA1kCID,gBAAgB;EAAA,QAKPf,cAAc,EACDA,cAAc,EAIbA,cAAc,EAGPA,cAAc,EAGnBA,cAAc,EASvCA,cAAc,EAEDJ,WAAW,EACXK,cAAc;AAAA;AAAA0R,EAAA,GA5B3B5Q,gBAAgB;AA4kCtB,eAAeA,gBAAgB;AAAC,IAAA4Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"E:\\\\Uroom\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\BookingCheckPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, use } from \"react\";\nimport { Container, Row, Col, Card, Form, Button, InputGroup } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\nimport Banner from \"../../../images/banner.jpg\";\nimport Header from \"../Header\";\nimport Footer from \"../Footer\";\nimport * as Routers from \"../../../utils/Routes\";\nimport { useNavigate } from \"react-router-dom\";\nimport ConfirmationModal from \"@components/ConfirmationModal\";\nimport PromotionModal from \"./components/PromotionModal\";\nimport PromotionErrorModal from \"./components/PromotionErrorModal\";\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\nimport { applyPromotion } from \"../../../redux/promotion/actions\";\nimport Utils from \"../../../utils/Utils\";\nimport Factories from \"../../../redux/search/factories\";\nimport { ChatBox } from \"./HomePage\";\nimport SearchActions from \"../../../redux/search/actions\";\nimport HotelActions from \"@redux/hotel/actions\";\nimport HotelClosedModal from \"./components/HotelClosedModal\";\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\nimport getApiBackendUrl from \"@utils/apiConfig\";\nimport RoomClosedModal from \"./components/RoomClosedModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookingCheckPage = () => {\n  _s();\n  var _hotelDetail$hotelNam, _hotelDetail$address;\n  const API_BASE_URL = getApiBackendUrl(); // Add this line\n\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\n  const [error, setError] = useState(null);\n  const Auth = useAppSelector(state => state.Auth.Auth);\n  const SearchInformation = useAppSelector(state => state.Search.SearchInformation);\n  const selectedRoomsTemps = useAppSelector(state => state.Search.selectedRooms);\n  const selectedServicesFromRedux = useAppSelector(state => state.Search.selectedServices);\n  const hotelDetailFromRedux = useAppSelector(state => state.Search.hotelDetail);\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\n\n  // Promotion code state\n  const [promotionCode, setPromotionCode] = useState(\"\");\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\n  const [promotionId, setPromotionId] = useState(null);\n\n  // Add state for booking data\n  const [bookingData, setBookingData] = useState({\n    selectedRooms: selectedRoomsTemps || [],\n    selectedServices: selectedServicesFromRedux || [],\n    hotelDetail: hotelDetailFromRedux || null,\n    searchInfo: SearchInformation\n  });\n  const [dataRestored, setDataRestored] = useState(false);\n  const [isValidatingPromotion, setIsValidatingPromotion] = useState(false);\n  const [isCheckingHotelStatus, setIsCheckingHotelStatus] = useState(false);\n  const [isValidatingPromotionBeforeBooking, setIsValidatingPromotionBeforeBooking] = useState(false);\n  const [isInitialLoading, setIsInitialLoading] = useState(true);\n\n  // Restore data from sessionStorage stack when component mounts\n  useEffect(() => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      const currentBooking = bookingStack[bookingStack.length - 1];\n      setBookingData(currentBooking);\n\n      // Update Redux store with current data\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: currentBooking.selectedRooms,\n          selectedServices: currentBooking.selectedServices,\n          hotelDetail: currentBooking.hotelDetail\n        }\n      });\n    }\n    setDataRestored(true);\n    setIsInitialLoading(false);\n  }, [dispatch]);\n\n  // Load promotion info from sessionStorage AFTER booking data is restored\n  useEffect(() => {\n    if (dataRestored) {\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\n      if (promo) {\n        var _bookingData$hotelDet, _bookingData$selected;\n        // Check if this is a new booking (different hotel or rooms)\n        const currentHotelId = (_bookingData$hotelDet = bookingData.hotelDetail) === null || _bookingData$hotelDet === void 0 ? void 0 : _bookingData$hotelDet._id;\n        const savedHotelId = promo.hotelId;\n        const currentRoomsHash = JSON.stringify((_bookingData$selected = bookingData.selectedRooms) === null || _bookingData$selected === void 0 ? void 0 : _bookingData$selected.map(r => ({\n          roomId: r.room._id,\n          amount: r.amount\n        })).sort());\n        const savedRoomsHash = promo.roomsHash;\n        if (currentHotelId !== savedHotelId || currentRoomsHash !== savedRoomsHash) {\n          // This is a new booking, clear old promotion\n          sessionStorage.removeItem(\"promotionInfo\");\n          console.log(\"🆕 New booking detected, cleared old promotion\");\n          return;\n        }\n\n        // Check if promotion was saved more than 5 minutes ago\n        const savedTime = promo.savedTime || Date.now();\n        const timeDiff = Date.now() - savedTime;\n        const fiveMinutes = 5 * 60 * 1000;\n        if (timeDiff > fiveMinutes) {\n          // Auto-validate if promotion is old\n          console.log(\"Promotion is old, auto-validating...\");\n          setPromotionCode(promo.promotionCode || \"\");\n          setPromotionDiscount(promo.promotionDiscount || 0);\n          setPromotionMessage(\"Validating promotion...\");\n          setPromotionId(promo.promotionId || null);\n        } else {\n          setPromotionCode(promo.promotionCode || \"\");\n          setPromotionDiscount(promo.promotionDiscount || 0);\n          setPromotionMessage(promo.promotionMessage || \"\");\n          setPromotionId(promo.promotionId || null);\n          console.log(\"🔄 Restored promotion for same booking:\", promo.promotionCode);\n        }\n      }\n    }\n  }, [dataRestored, bookingData.hotelDetail, bookingData.selectedRooms]);\n\n  // Save promotion info to sessionStorage when any promotion state changes\n  useEffect(() => {\n    if (dataRestored) {\n      var _bookingData$hotelDet2, _bookingData$selected2;\n      // Chỉ save khi đã restore xong data\n      sessionStorage.setItem(\"promotionInfo\", JSON.stringify({\n        promotionCode,\n        promotionDiscount,\n        promotionMessage,\n        promotionId,\n        savedTime: Date.now(),\n        // Add timestamp for validation\n        // Save booking context to detect new bookings\n        hotelId: (_bookingData$hotelDet2 = bookingData.hotelDetail) === null || _bookingData$hotelDet2 === void 0 ? void 0 : _bookingData$hotelDet2._id,\n        roomsHash: JSON.stringify((_bookingData$selected2 = bookingData.selectedRooms) === null || _bookingData$selected2 === void 0 ? void 0 : _bookingData$selected2.map(r => ({\n          roomId: r.room._id,\n          amount: r.amount\n        })).sort())\n      }));\n    }\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored, bookingData.hotelDetail, bookingData.selectedRooms]);\n\n  // Use bookingData instead of Redux state\n  const selectedRooms = bookingData.selectedRooms;\n  const selectedServices = bookingData.selectedServices;\n  const hotelDetail = bookingData.hotelDetail;\n  const searchInfo = bookingData.searchInfo;\n\n  // Calculate number of days between check-in and check-out\n  const calculateNumberOfDays = () => {\n    const checkIn = new Date(searchInfo.checkinDate);\n    const checkOut = new Date(searchInfo.checkoutDate);\n    const diffTime = Math.abs(checkOut - checkIn);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n  const numberOfDays = calculateNumberOfDays();\n\n  // Calculate prices\n  const totalRoomPrice = selectedRooms.reduce((total, {\n    room,\n    amount\n  }) => total + room.price * amount * numberOfDays, 0);\n  const totalServicePrice = selectedServices.reduce((total, service) => {\n    const selectedDates = service.selectedDates || [];\n    const serviceQuantity = service.quantity * selectedDates.length;\n    return total + service.price * serviceQuantity;\n  }, 0);\n  const subtotal = totalRoomPrice + totalServicePrice;\n  const finalPrice = Math.max(subtotal - promotionDiscount, 0);\n\n  // Validate promotion when data is restored or booking changes\n  useEffect(() => {\n    if (!dataRestored || !promotionCode || !promotionId || promotionDiscount === 0) return;\n\n    // Add a small delay to ensure promotion is fully restored before validation\n    const timeoutId = setTimeout(() => {\n      const validatePromotion = async () => {\n        // Only show loading if validation takes longer than 200ms\n        const loadingTimeoutId = setTimeout(() => {\n          setIsValidatingPromotion(true);\n        }, 200);\n        try {\n          const res = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\n            code: promotionCode,\n            orderAmount: subtotal\n          });\n          clearTimeout(loadingTimeoutId);\n          if (!res.data.valid || res.data.discount !== promotionDiscount) {\n            // Batch update all promotion states to minimize re-renders\n            setTimeout(() => {\n              setPromotionCode(\"\");\n              setPromotionDiscount(0);\n              setPromotionMessage(\"Promotion is no longer valid due to booking changes\");\n              setPromotionId(null);\n              sessionStorage.removeItem(\"promotionInfo\");\n            }, 0);\n          }\n        } catch (err) {\n          clearTimeout(loadingTimeoutId);\n          // Batch update all promotion states to minimize re-renders\n          setTimeout(() => {\n            setPromotionCode(\"\");\n            setPromotionDiscount(0);\n            setPromotionMessage(\"Promotion is no longer valid\");\n            setPromotionId(null);\n            sessionStorage.removeItem(\"promotionInfo\");\n          }, 0);\n        } finally {\n          setIsValidatingPromotion(false);\n        }\n      };\n      validatePromotion();\n    }, 100);\n    return () => clearTimeout(timeoutId);\n  }, [dataRestored, subtotal, promotionCode, promotionId, promotionDiscount]); // Validate when subtotal changes or data is restored\n\n  // Handle navigation back to HomeDetailPage\n  const handleBackToHomeDetail = () => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      // Remove the current booking from stack\n      bookingStack.pop();\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n    }\n    // Don't clear promotion here - user might come back with same selection\n    // Promotion will be cleared only when new booking (different hotel/rooms) is detected\n    navigate(-1);\n  };\n\n  // Star rating component\n  const StarRating = ({\n    rating\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"star-rating\",\n      children: [...Array(5)].map((_, index) => index < rating ? /*#__PURE__*/_jsxDEV(FaStar, {\n        className: \"star filled\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(FaRegStar, {\n        className: \"star\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this);\n  };\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\n  const [showPromotionErrorModal, setShowPromotionErrorModal] = useState(false);\n  const [promotionErrorMessage, setPromotionErrorMessage] = useState(\"\");\n  const [invalidPromotionCode, setInvalidPromotionCode] = useState(\"\");\n\n  // Add state for payment error modal\n  const [showPaymentErrorModal, setShowPaymentErrorModal] = useState(false);\n  const [paymentErrorMessage, setPaymentErrorMessage] = useState({\n    title: \"\",\n    mainMessage: \"\",\n    subMessage: \"\"\n  });\n\n  // Hàm xử lý áp dụng promotion từ modal với batch update để tránh multiple re-renders\n  const handleApplyPromotionFromModal = promotionData => {\n    // Batch update all promotion states at once to minimize re-renders\n    const updatePromotionStates = () => {\n      setPromotionCode(promotionData.code);\n      setPromotionDiscount(promotionData.discount);\n      setPromotionMessage(promotionData.message);\n      setPromotionId(promotionData.promotionId);\n    };\n\n    // Use setTimeout to batch the state updates\n    setTimeout(updatePromotionStates, 0);\n  };\n\n  // Function to validate promotion before booking (optimized to avoid unnecessary re-renders)\n  const validatePromotionBeforeBooking = async () => {\n    if (!promotionCode || !promotionId || promotionDiscount === 0) {\n      return {\n        valid: true\n      }; // No promotion to validate\n    }\n\n    // Only show loading state if validation takes longer than 300ms\n    let shouldShowLoading = false;\n    const loadingTimeoutId = setTimeout(() => {\n      shouldShowLoading = true;\n      setIsValidatingPromotionBeforeBooking(true);\n    }, 300);\n    try {\n      const res = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\n        code: promotionCode,\n        orderAmount: subtotal\n      });\n\n      // Clear timeout and loading state\n      clearTimeout(loadingTimeoutId);\n      if (shouldShowLoading) {\n        setIsValidatingPromotionBeforeBooking(false);\n      }\n      if (!res.data.valid) {\n        return {\n          valid: false,\n          message: res.data.message || \"Promotion is no longer valid\"\n        };\n      }\n      if (res.data.discount !== promotionDiscount) {\n        return {\n          valid: false,\n          message: \"Promotion discount has changed. Please reapply the promotion.\"\n        };\n      }\n      return {\n        valid: true\n      };\n    } catch (err) {\n      // Clear timeout and loading state\n      clearTimeout(loadingTimeoutId);\n      if (shouldShowLoading) {\n        setIsValidatingPromotionBeforeBooking(false);\n      }\n      return {\n        valid: false,\n        message: \"Unable to validate promotion. Please try again.\"\n      };\n    }\n  };\n\n  // Function to check hotel status before booking (optimized to avoid unnecessary loading states)\n  const checkHotelStatusBeforeBooking = async () => {\n    return new Promise((resolve, reject) => {\n      // Only show loading state if check takes longer than 300ms\n      let shouldShowLoading = false;\n      const loadingTimeoutId = setTimeout(() => {\n        shouldShowLoading = true;\n        setIsCheckingHotelStatus(true);\n      }, 300);\n      dispatch({\n        type: HotelActions.FETCH_DETAIL_HOTEL,\n        payload: {\n          hotelId: hotelDetail._id,\n          userId: Auth._id,\n          onSuccess: hotel => {\n            clearTimeout(loadingTimeoutId);\n            if (shouldShowLoading) {\n              setIsCheckingHotelStatus(false);\n            }\n            if (hotel.ownerStatus === \"ACTIVE\") {\n              resolve(hotel);\n            } else {\n              reject(new Error(\"Hotel is currently inactive\"));\n            }\n          },\n          onFailed: error => {\n            clearTimeout(loadingTimeoutId);\n            if (shouldShowLoading) {\n              setIsCheckingHotelStatus(false);\n            }\n            reject(new Error(error || \"Failed to check hotel status\"));\n          },\n          onError: () => {\n            clearTimeout(loadingTimeoutId);\n            if (shouldShowLoading) {\n              setIsCheckingHotelStatus(false);\n            }\n            reject(new Error(\"Server error while checking hotel status\"));\n          }\n        }\n      });\n    });\n  };\n  const createBooking = async () => {\n    try {\n      // Validate promotion first if there's one applied\n      const promotionValidation = await validatePromotionBeforeBooking();\n      if (!promotionValidation.valid) {\n        // Store error info for modal\n        setPromotionErrorMessage(promotionValidation.message);\n        setInvalidPromotionCode(promotionCode);\n\n        // Clear invalid promotion\n        setPromotionCode(\"\");\n        setPromotionDiscount(0);\n        setPromotionMessage(\"\");\n        setPromotionId(null);\n        sessionStorage.removeItem(\"promotionInfo\");\n\n        // Show error modal\n        setShowPromotionErrorModal(true);\n        return;\n      }\n\n      // Check hotel status\n      const hotel = await checkHotelStatusBeforeBooking();\n      console.log(\"Hotel detail fetched successfully:\", hotel);\n      const totalRoomPrice = selectedRooms.reduce((total, {\n        room,\n        amount\n      }) => total + room.price * amount * numberOfDays, 0);\n      const totalServicePrice = selectedServices.reduce((total, service) => {\n        const selectedDates = service.selectedDates || [];\n        const serviceQuantity = service.quantity * selectedDates.length;\n        return total + service.price * serviceQuantity;\n      }, 0);\n      const bookingSubtotal = totalRoomPrice + totalServicePrice;\n      const params = {\n        hotelId: hotelDetail._id,\n        checkOutDate: searchInfo.checkoutDate,\n        checkInDate: searchInfo.checkinDate,\n        totalPrice: bookingSubtotal,\n        // giá gốc\n        finalPrice: finalPrice,\n        // giá sau giảm giá\n        roomDetails: selectedRooms.map(({\n          room,\n          amount\n        }) => ({\n          room: {\n            _id: room._id\n          },\n          amount: amount\n        })),\n        serviceDetails: selectedServices.map(service => {\n          var _service$selectedDate;\n          return {\n            _id: service._id,\n            quantity: service.quantity * (((_service$selectedDate = service.selectedDates) === null || _service$selectedDate === void 0 ? void 0 : _service$selectedDate.length) || 0),\n            selectDate: service.selectedDates || []\n          };\n        }),\n        // Thêm promotionId và promotionDiscount nếu có\n        ...(promotionId && {\n          promotionId\n        }),\n        ...(promotionDiscount > 0 && {\n          promotionDiscount\n        })\n      };\n      console.log(\"params >> \", params);\n\n      // Helper function to save reservationId to bookingStack\n      const saveReservationIdToBookingStack = reservationId => {\n        if (reservationId) {\n          const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n          if (bookingStack.length > 0) {\n            bookingStack[bookingStack.length - 1].reservationId = reservationId;\n            sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n          }\n        }\n      };\n      try {\n        let reservationId = null;\n        const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n        if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\n          reservationId = bookingStack[bookingStack.length - 1].reservationId;\n        }\n        const response = await Factories.create_booking({\n          ...params,\n          reservationId\n        });\n        console.log(\"response >> \", response);\n        if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n          var _response$data, _response$data$unpaid, _responseCheckout$dat;\n          reservationId = response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : (_response$data$unpaid = _response$data.unpaidReservation) === null || _response$data$unpaid === void 0 ? void 0 : _response$data$unpaid._id;\n          saveReservationIdToBookingStack(reservationId);\n          const unpaidReservationId = reservationId;\n          const responseCheckout = await Factories.checkout_booking(unpaidReservationId);\n          console.log(\"responseCheckout >> \", responseCheckout);\n          const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat = responseCheckout.data) === null || _responseCheckout$dat === void 0 ? void 0 : _responseCheckout$dat.sessionUrl;\n          if (paymentUrl) {\n            // Don't clear promotion here - user might come back from payment\n            // Promotion will be cleared when new booking is created\n            window.location.href = paymentUrl;\n          }\n        } else if ((response === null || response === void 0 ? void 0 : response.status) === 201) {\n          var _response$data2, _response$data2$reser, _responseCheckout$dat2;\n          reservationId = response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : (_response$data2$reser = _response$data2.reservation) === null || _response$data2$reser === void 0 ? void 0 : _response$data2$reser._id;\n          saveReservationIdToBookingStack(reservationId);\n          const responseCheckout = await Factories.checkout_booking(reservationId);\n          const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat2 = responseCheckout.data) === null || _responseCheckout$dat2 === void 0 ? void 0 : _responseCheckout$dat2.sessionUrl;\n          if (paymentUrl) {\n            // Don't clear promotion here - user might come back from payment\n            // Promotion will be cleared when new booking is created\n            window.location.href = paymentUrl;\n          }\n        } else {\n          showToast.error(\"error create booking\");\n        }\n      } catch (error) {\n        var _error$response, _error$response$data, _error$response2, _error$response2$data;\n        console.error(\"Error create payment: \", (_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message);\n        setPaymentErrorMessage({\n          title: \"Payment Error\",\n          mainMessage: \"Unable to process your payment at this time\",\n          subMessage: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Please try again later\"\n        });\n        setShowPaymentErrorModal(true);\n      }\n    } catch (error) {\n      console.error(\"Error checking hotel status:\", error);\n      setShowModalStatusBooking(true);\n    }\n  };\n  const handleAccept = async () => {\n    const totalRoomPrice = selectedRooms.reduce((total, {\n      room,\n      amount\n    }) => total + room.price * amount * numberOfDays, 0);\n    if (totalRoomPrice > 0) {\n      // Final validation before creating booking\n      await createBooking();\n\n      // Only clear selection if booking was successful\n      // (createBooking will handle errors and not reach this point if failed)\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: [],\n          selectedServices: [],\n          hotelDetail: hotelDetail\n        }\n      });\n    }\n  };\n  const handleConfirmBooking = () => {\n    setShowAcceptModal(true);\n  };\n\n  // Only show loading spinner during initial load, not during re-renders\n  if (isInitialLoading || !hotelDetail && !dataRestored) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: \"100vh\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 599,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If data is restored but hotelDetail is still missing, redirect back\n  if (!hotelDetail && dataRestored) {\n    navigate(-1);\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex flex-column min-vh-100\",\n    style: {\n      backgroundImage: `url(${Banner})`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\",\n      style: {\n        paddingTop: \"65px\",\n        paddingBottom: \"50px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Container, {\n        className: \"mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(ToastProvider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"booking-card text-white\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                marginBottom: \"20px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stars mb-2\",\n                style: {\n                  justifyContent: \"flex-start\",\n                  justifyItems: \"self-start\"\n                },\n                children: /*#__PURE__*/_jsxDEV(StarRating, {\n                  rating: hotelDetail.star\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"hotel-name mb-1\",\n                children: (_hotelDetail$hotelNam = hotelDetail.hotelName) !== null && _hotelDetail$hotelNam !== void 0 ? _hotelDetail$hotelNam : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hotel-address small mb-4\",\n                children: (_hotelDetail$address = hotelDetail.address) !== null && _hotelDetail$address !== void 0 ? _hotelDetail$address : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-4\",\n                children: \"Your booking detail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkin\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkinDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkout\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkout\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 689,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkoutDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 695,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stay-info mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total length of stay:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [numberOfDays, \" night\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 705,\n                    columnNumber: 21\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total number of people:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [searchInfo.adults, \" Adults - \", searchInfo.childrens, \" \", \"Childrens\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 709,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-room mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-4\",\n                  children: \"You selected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 19\n                }, this), selectedRooms.map(({\n                  room,\n                  amount\n                }) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [amount, \" x \", room.name, \" (\", numberOfDays, \" days):\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 733,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: Utils.formatCurrency(room.price * amount * numberOfDays)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 23\n                  }, this)]\n                }, room._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 21\n                }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: handleBackToHomeDetail,\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 745,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 17\n              }, this), selectedServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-services mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-3\",\n                  children: \"Selected Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 758,\n                  columnNumber: 21\n                }, this), selectedServices.map(service => {\n                  const selectedDates = service.selectedDates || [];\n                  const serviceQuantity = service.quantity * selectedDates.length;\n                  const serviceTotal = service.price * serviceQuantity;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [service.quantity, \" x \", service.name, \" (\", selectedDates.length, \" days):\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 771,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"fw-bold\",\n                      children: Utils.formatCurrency(serviceTotal)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 775,\n                      columnNumber: 27\n                    }, this)]\n                  }, service._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 25\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: () => {\n                      dispatch({\n                        type: SearchActions.SAVE_SELECTED_ROOMS,\n                        payload: {\n                          selectedRooms: selectedRooms,\n                          selectedServices: selectedServices,\n                          hotelDetail: hotelDetail\n                        }\n                      });\n                      navigate(-1);\n                    },\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-section mb-3\",\n                children: [promotionDiscount > 0 ? /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"promotion-applied mb-3\",\n                  style: {\n                    backgroundColor: \"rgba(40, 167, 69, 0.2)\",\n                    borderColor: \"#28a745\",\n                    border: \"2px solid #28a745\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"py-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                            className: \"text-success me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 828,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"fw-bold text-success\",\n                            children: promotionCode\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 829,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 827,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-success\",\n                          children: [\"Save \", Utils.formatCurrency(promotionDiscount)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 833,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 826,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-danger\",\n                        size: \"sm\",\n                        onClick: () => handleApplyPromotionFromModal({\n                          code: \"\",\n                          discount: 0,\n                          message: \"\",\n                          promotionId: null\n                        }),\n                        className: \"d-flex align-items-center\",\n                        disabled: isValidatingPromotion || isValidatingPromotionBeforeBooking,\n                        children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 854,\n                          columnNumber: 29\n                        }, this), isValidatingPromotion || isValidatingPromotionBeforeBooking ? \"...\" : \"Remove\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 837,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 825,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 824,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 816,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-3 mb-3\",\n                  style: {\n                    border: \"2px dashed rgba(255,255,255,0.3)\",\n                    borderRadius: \"8px\",\n                    backgroundColor: \"rgba(255,255,255,0.05)\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"text-muted mb-2\",\n                    size: 24\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-muted small\",\n                    children: \"No promotion applied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 873,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 864,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-light\",\n                  className: \"w-100 d-flex align-items-center justify-content-center\",\n                  onClick: () => setShowPromotionModal(true),\n                  style: {\n                    borderStyle: \"dashed\",\n                    borderWidth: \"2px\",\n                    padding: \"12px\"\n                  },\n                  disabled: isValidatingPromotion || isValidatingPromotionBeforeBooking,\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 894,\n                    columnNumber: 21\n                  }, this), isValidatingPromotion || isValidatingPromotionBeforeBooking ? \"Validating...\" : promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 880,\n                  columnNumber: 19\n                }, this), (isValidatingPromotion || isValidatingPromotionBeforeBooking) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mt-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"spinner-border spinner-border-sm me-1\",\n                      role: \"status\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"visually-hidden\",\n                        children: \"Loading...\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 911,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 907,\n                      columnNumber: 25\n                    }, this), \"Checking promotion validity...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 906,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 905,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-breakdown\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Subtotal:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 922,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: Utils.formatCurrency(subtotal)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 923,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 19\n                }, this), promotionDiscount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success\",\n                    children: \"Discount:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold text-success\",\n                    children: [\"-\", Utils.formatCurrency(promotionDiscount)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 929,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"booking-divider mb-2\",\n                  style: {\n                    height: \"1px\",\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                    margin: \"10px 0\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-danger mb-0\",\n                    children: [\"Total: \", Utils.formatCurrency(finalPrice)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 947,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small\",\n                  children: \"Includes taxes and fees\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 951,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                color: \"white\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-4\",\n                children: \"Check your information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 967,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Full name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 971,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    value: Auth.name,\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 972,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 984,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"email\",\n                    value: Auth.email,\n                    placeholder: \"<EMAIL>\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 985,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 983,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 998,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"tel\",\n                    value: Auth.phoneNumber,\n                    placeholder: \"0912345678\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 999,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 997,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Who are you booking for?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1012,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"mainGuest\",\n                      label: \"I'm the main guest\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"mainGuest\",\n                      onChange: () => setBookingFor(\"mainGuest\"),\n                      className: \"mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1014,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"someoneElse\",\n                      label: \"I'm booking for someone else\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"someoneElse\",\n                      onChange: () => setBookingFor(\"someoneElse\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1023,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1013,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1011,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    className: \"px-4 py-2\",\n                    style: {\n                      borderRadius: \"10px\",\n                      backgroundColor: \"white\",\n                      color: \"#007bff\",\n                      border: \"none\",\n                      fontWeight: \"bold\"\n                    },\n                    onClick: handleConfirmBooking,\n                    disabled: isCheckingHotelStatus || isValidatingPromotion || isValidatingPromotionBeforeBooking,\n                    children: isValidatingPromotionBeforeBooking ? \"Validating Promotion...\" : isCheckingHotelStatus ? \"Checking Hotel...\" : \"Booking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1035,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n                    show: showAcceptModal,\n                    onHide: () => setShowAcceptModal(false),\n                    onConfirm: handleAccept,\n                    title: \"Confirm Acceptance\",\n                    message: \"Do you want to proceed with this booking confirmation?\",\n                    confirmButtonText: \"Accept\",\n                    type: \"accept\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1058,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(ChatBox, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1074,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1073,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 626,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1077,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionModal, {\n      show: showPromotionModal,\n      onHide: () => setShowPromotionModal(false),\n      totalPrice: subtotal,\n      onApplyPromotion: handleApplyPromotionFromModal,\n      currentPromotionId: promotionId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1080,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HotelClosedModal, {\n      show: showModalStatusBooking,\n      onClose: () => {\n        setShowModalStatusBooking(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1088,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionErrorModal, {\n      show: showPromotionErrorModal,\n      onClose: () => {\n        setShowPromotionErrorModal(false);\n        setPromotionErrorMessage(\"\");\n        setInvalidPromotionCode(\"\");\n      },\n      onSelectNewPromotion: () => {\n        setShowPromotionErrorModal(false);\n        setPromotionErrorMessage(\"\");\n        setInvalidPromotionCode(\"\");\n        setShowPromotionModal(true);\n      },\n      errorMessage: promotionErrorMessage,\n      promotionCode: invalidPromotionCode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1096,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RoomClosedModal, {\n      show: showPaymentErrorModal,\n      onClose: () => setShowPaymentErrorModal(false),\n      title: paymentErrorMessage.title,\n      mainMessage: paymentErrorMessage.mainMessage,\n      subMessage: paymentErrorMessage.subMessage,\n      buttonText: \"Try Again\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1113,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 617,\n    columnNumber: 5\n  }, this);\n};\n_s(BookingCheckPage, \"XI+V80sE92HRGR5kKRCJdDLl7yc=\", false, function () {\n  return [useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useNavigate, useAppDispatch];\n});\n_c = BookingCheckPage;\nexport default BookingCheckPage;\nvar _c;\n$RefreshReg$(_c, \"BookingCheckPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "use", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "InputGroup", "FaStar", "FaRegStar", "FaTag", "FaTimes", "Banner", "Header", "Footer", "Routers", "useNavigate", "ConfirmationModal", "PromotionModal", "PromotionErrorModal", "useAppSelector", "useAppDispatch", "applyPromotion", "Utils", "Factories", "ChatBox", "SearchActions", "HotelActions", "HotelClosedModal", "showToast", "ToastProvider", "getApiBackendUrl", "RoomClosedModal", "jsxDEV", "_jsxDEV", "BookingCheckPage", "_s", "_hotelDetail$hotelNam", "_hotelDetail$address", "API_BASE_URL", "showModalStatusBooking", "setShowModalStatusBooking", "error", "setError", "<PERSON><PERSON>", "state", "SearchInformation", "Search", "selectedRoomsTemps", "selectedRooms", "selectedServicesFromRedux", "selectedServices", "hotelDetailFromRedux", "hotelDetail", "navigate", "dispatch", "bookingFor", "setBookingFor", "promotionCode", "setPromotionCode", "promotionDiscount", "setPromotionDiscount", "promotionMessage", "setPromotionMessage", "promotionId", "setPromotionId", "bookingData", "setBookingData", "searchInfo", "dataRestored", "setDataRestored", "isValidatingPromotion", "setIsValidatingPromotion", "isCheckingHotelStatus", "setIsCheckingHotelStatus", "isValidatingPromotionBeforeBooking", "setIsValidatingPromotionBeforeBooking", "isInitialLoading", "setIsInitialLoading", "bookingStack", "JSON", "parse", "sessionStorage", "getItem", "length", "currentBooking", "type", "SAVE_SELECTED_ROOMS", "payload", "promo", "_bookingData$hotelDet", "_bookingData$selected", "currentHotelId", "_id", "savedHotelId", "hotelId", "currentRoomsHash", "stringify", "map", "r", "roomId", "room", "amount", "sort", "savedRoomsHash", "roomsHash", "removeItem", "console", "log", "savedTime", "Date", "now", "timeDiff", "fiveMinutes", "_bookingData$hotelDet2", "_bookingData$selected2", "setItem", "calculateNumberOfDays", "checkIn", "checkinDate", "checkOut", "checkoutDate", "diffTime", "Math", "abs", "diffDays", "ceil", "numberOfDays", "totalRoomPrice", "reduce", "total", "price", "totalServicePrice", "service", "selectedDates", "serviceQuantity", "quantity", "subtotal", "finalPrice", "max", "timeoutId", "setTimeout", "validatePromotion", "loadingTimeoutId", "res", "axios", "post", "code", "orderAmount", "clearTimeout", "data", "valid", "discount", "err", "handleBackToHomeDetail", "pop", "StarRating", "rating", "className", "children", "Array", "_", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showAcceptModal", "setShowAcceptModal", "showPromotionModal", "setShowPromotionModal", "showPromotionErrorModal", "setShowPromotionErrorModal", "promotionErrorMessage", "setPromotionErrorMessage", "invalidPromotionCode", "setInvalidPromotionCode", "showPaymentErrorModal", "setShowPaymentErrorModal", "paymentErrorMessage", "setPaymentErrorMessage", "title", "mainMessage", "subMessage", "handleApplyPromotionFromModal", "promotionData", "updatePromotionStates", "message", "validatePromotionBeforeBooking", "shouldShowLoading", "checkHotelStatusBeforeBooking", "Promise", "resolve", "reject", "FETCH_DETAIL_HOTEL", "userId", "onSuccess", "hotel", "ownerStatus", "Error", "onFailed", "onError", "createBooking", "promotionValidation", "bookingSubtotal", "params", "checkOutDate", "checkInDate", "totalPrice", "roomDetails", "serviceDetails", "_service$selectedDate", "selectDate", "saveReservationIdToBookingStack", "reservationId", "response", "create_booking", "status", "_response$data", "_response$data$unpaid", "_responseCheckout$dat", "unpaidReservation", "unpaidReservationId", "responseCheckout", "checkout_booking", "paymentUrl", "sessionUrl", "window", "location", "href", "_response$data2", "_response$data2$reser", "_responseCheckout$dat2", "reservation", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "handleAccept", "handleConfirmBooking", "style", "height", "role", "backgroundImage", "backgroundSize", "backgroundPosition", "paddingTop", "paddingBottom", "md", "lg", "backgroundColor", "borderRadius", "padding", "marginBottom", "justifyContent", "justifyItems", "star", "hotelName", "address", "margin", "xs", "fontSize", "getDate", "adults", "childrens", "name", "formatCurrency", "cursor", "onClick", "serviceTotal", "borderColor", "border", "Body", "variant", "size", "disabled", "borderStyle", "borderWidth", "color", "Group", "Label", "Control", "value", "email", "placeholder", "phoneNumber", "Check", "id", "label", "checked", "onChange", "fontWeight", "show", "onHide", "onConfirm", "confirmButtonText", "onApplyPromotion", "currentPromotionId", "onClose", "onSelectNewPromotion", "errorMessage", "buttonText", "_c", "$RefreshReg$"], "sources": ["E:/Uroom/Customer/src/pages/customer/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect, use } from \"react\";\r\nimport {\r\n  Container,\r\n  Row,\r\n  Col,\r\n  Card,\r\n  Form,\r\n  Button,\r\n  InputGroup,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\r\nimport Banner from \"../../../images/banner.jpg\";\r\nimport Header from \"../Header\";\r\nimport Footer from \"../Footer\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport ConfirmationModal from \"@components/ConfirmationModal\";\r\nimport PromotionModal from \"./components/PromotionModal\";\r\nimport PromotionErrorModal from \"./components/PromotionErrorModal\";\r\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\r\nimport { applyPromotion } from \"../../../redux/promotion/actions\";\r\nimport Utils from \"../../../utils/Utils\";\r\nimport Factories from \"../../../redux/search/factories\";\r\nimport { ChatBox } from \"./HomePage\";\r\nimport SearchActions from \"../../../redux/search/actions\";\r\nimport HotelActions from \"@redux/hotel/actions\";\r\nimport HotelClosedModal from \"./components/HotelClosedModal\";\r\nimport { showToast, ToastProvider } from \"@components/ToastContainer\";\r\nimport getApiBackendUrl from \"@utils/apiConfig\";\r\nimport RoomClosedModal from \"./components/RoomClosedModal\";\r\n\r\nconst BookingCheckPage = () => {\r\n  const API_BASE_URL = getApiBackendUrl(); // Add this line\r\n\r\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\r\n  const [error, setError]= useState(null);\r\n  const Auth = useAppSelector((state) => state.Auth.Auth);\r\n  const SearchInformation = useAppSelector(\r\n    (state) => state.Search.SearchInformation\r\n  );\r\n\r\n  const selectedRoomsTemps = useAppSelector(\r\n    (state) => state.Search.selectedRooms\r\n  );\r\n  const selectedServicesFromRedux = useAppSelector(\r\n    (state) => state.Search.selectedServices\r\n  );\r\n  const hotelDetailFromRedux = useAppSelector(\r\n    (state) => state.Search.hotelDetail\r\n  );\r\n  const navigate = useNavigate();\r\n  const dispatch = useAppDispatch();\r\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\r\n\r\n  // Promotion code state\r\n  const [promotionCode, setPromotionCode] = useState(\"\");\r\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\r\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\r\n  const [promotionId, setPromotionId] = useState(null);\r\n\r\n  // Add state for booking data\r\n  const [bookingData, setBookingData] = useState({\r\n    selectedRooms: selectedRoomsTemps || [],\r\n    selectedServices: selectedServicesFromRedux || [],\r\n    hotelDetail: hotelDetailFromRedux || null,\r\n    searchInfo: SearchInformation,\r\n  });\r\n\r\n  const [dataRestored, setDataRestored] = useState(false);\r\n  const [isValidatingPromotion, setIsValidatingPromotion] = useState(false);\r\n  const [isCheckingHotelStatus, setIsCheckingHotelStatus] = useState(false);\r\n  const [\r\n    isValidatingPromotionBeforeBooking,\r\n    setIsValidatingPromotionBeforeBooking,\r\n  ] = useState(false);\r\n  const [isInitialLoading, setIsInitialLoading] = useState(true);\r\n\r\n  // Restore data from sessionStorage stack when component mounts\r\n  useEffect(() => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      const currentBooking = bookingStack[bookingStack.length - 1];\r\n      setBookingData(currentBooking);\r\n\r\n      // Update Redux store with current data\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: currentBooking.selectedRooms,\r\n          selectedServices: currentBooking.selectedServices,\r\n          hotelDetail: currentBooking.hotelDetail,\r\n        },\r\n      });\r\n    }\r\n    setDataRestored(true);\r\n    setIsInitialLoading(false);\r\n  }, [dispatch]);\r\n\r\n  // Load promotion info from sessionStorage AFTER booking data is restored\r\n  useEffect(() => {\r\n    if (dataRestored) {\r\n      const promo = JSON.parse(\r\n        sessionStorage.getItem(\"promotionInfo\") || \"null\"\r\n      );\r\n      if (promo) {\r\n        // Check if this is a new booking (different hotel or rooms)\r\n        const currentHotelId = bookingData.hotelDetail?._id;\r\n        const savedHotelId = promo.hotelId;\r\n        const currentRoomsHash = JSON.stringify(\r\n          bookingData.selectedRooms\r\n            ?.map((r) => ({ roomId: r.room._id, amount: r.amount }))\r\n            .sort()\r\n        );\r\n        const savedRoomsHash = promo.roomsHash;\r\n\r\n        if (\r\n          currentHotelId !== savedHotelId ||\r\n          currentRoomsHash !== savedRoomsHash\r\n        ) {\r\n          // This is a new booking, clear old promotion\r\n          sessionStorage.removeItem(\"promotionInfo\");\r\n          console.log(\"🆕 New booking detected, cleared old promotion\");\r\n          return;\r\n        }\r\n\r\n        // Check if promotion was saved more than 5 minutes ago\r\n        const savedTime = promo.savedTime || Date.now();\r\n        const timeDiff = Date.now() - savedTime;\r\n        const fiveMinutes = 5 * 60 * 1000;\r\n\r\n        if (timeDiff > fiveMinutes) {\r\n          // Auto-validate if promotion is old\r\n          console.log(\"Promotion is old, auto-validating...\");\r\n          setPromotionCode(promo.promotionCode || \"\");\r\n          setPromotionDiscount(promo.promotionDiscount || 0);\r\n          setPromotionMessage(\"Validating promotion...\");\r\n          setPromotionId(promo.promotionId || null);\r\n        } else {\r\n          setPromotionCode(promo.promotionCode || \"\");\r\n          setPromotionDiscount(promo.promotionDiscount || 0);\r\n          setPromotionMessage(promo.promotionMessage || \"\");\r\n          setPromotionId(promo.promotionId || null);\r\n          console.log(\r\n            \"🔄 Restored promotion for same booking:\",\r\n            promo.promotionCode\r\n          );\r\n        }\r\n      }\r\n    }\r\n  }, [dataRestored, bookingData.hotelDetail, bookingData.selectedRooms]);\r\n\r\n  // Save promotion info to sessionStorage when any promotion state changes\r\n  useEffect(() => {\r\n    if (dataRestored) {\r\n      // Chỉ save khi đã restore xong data\r\n      sessionStorage.setItem(\r\n        \"promotionInfo\",\r\n        JSON.stringify({\r\n          promotionCode,\r\n          promotionDiscount,\r\n          promotionMessage,\r\n          promotionId,\r\n          savedTime: Date.now(), // Add timestamp for validation\r\n          // Save booking context to detect new bookings\r\n          hotelId: bookingData.hotelDetail?._id,\r\n          roomsHash: JSON.stringify(\r\n            bookingData.selectedRooms\r\n              ?.map((r) => ({ roomId: r.room._id, amount: r.amount }))\r\n              .sort()\r\n          ),\r\n        })\r\n      );\r\n    }\r\n  }, [\r\n    promotionCode,\r\n    promotionDiscount,\r\n    promotionMessage,\r\n    promotionId,\r\n    dataRestored,\r\n    bookingData.hotelDetail,\r\n    bookingData.selectedRooms,\r\n  ]);\r\n\r\n  // Use bookingData instead of Redux state\r\n  const selectedRooms = bookingData.selectedRooms;\r\n  const selectedServices = bookingData.selectedServices;\r\n  const hotelDetail = bookingData.hotelDetail;\r\n  const searchInfo = bookingData.searchInfo;\r\n\r\n  // Calculate number of days between check-in and check-out\r\n  const calculateNumberOfDays = () => {\r\n    const checkIn = new Date(searchInfo.checkinDate);\r\n    const checkOut = new Date(searchInfo.checkoutDate);\r\n    const diffTime = Math.abs(checkOut - checkIn);\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n    return diffDays;\r\n  };\r\n\r\n  const numberOfDays = calculateNumberOfDays();\r\n\r\n  // Calculate prices\r\n  const totalRoomPrice = selectedRooms.reduce(\r\n    (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n    0\r\n  );\r\n  const totalServicePrice = selectedServices.reduce((total, service) => {\r\n    const selectedDates = service.selectedDates || [];\r\n    const serviceQuantity = service.quantity * selectedDates.length;\r\n    return total + service.price * serviceQuantity;\r\n  }, 0);\r\n  const subtotal = totalRoomPrice + totalServicePrice;\r\n  const finalPrice = Math.max(subtotal - promotionDiscount, 0);\r\n\r\n  // Validate promotion when data is restored or booking changes\r\n  useEffect(() => {\r\n    if (\r\n      !dataRestored ||\r\n      !promotionCode ||\r\n      !promotionId ||\r\n      promotionDiscount === 0\r\n    )\r\n      return;\r\n\r\n    // Add a small delay to ensure promotion is fully restored before validation\r\n    const timeoutId = setTimeout(() => {\r\n      const validatePromotion = async () => {\r\n        // Only show loading if validation takes longer than 200ms\r\n        const loadingTimeoutId = setTimeout(() => {\r\n          setIsValidatingPromotion(true);\r\n        }, 200);\r\n\r\n        try {\r\n          const res = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\r\n            code: promotionCode,\r\n            orderAmount: subtotal,\r\n          });\r\n\r\n          clearTimeout(loadingTimeoutId);\r\n\r\n          if (!res.data.valid || res.data.discount !== promotionDiscount) {\r\n            // Batch update all promotion states to minimize re-renders\r\n            setTimeout(() => {\r\n              setPromotionCode(\"\");\r\n              setPromotionDiscount(0);\r\n              setPromotionMessage(\r\n                \"Promotion is no longer valid due to booking changes\"\r\n              );\r\n              setPromotionId(null);\r\n              sessionStorage.removeItem(\"promotionInfo\");\r\n            }, 0);\r\n          }\r\n        } catch (err) {\r\n          clearTimeout(loadingTimeoutId);\r\n          // Batch update all promotion states to minimize re-renders\r\n          setTimeout(() => {\r\n            setPromotionCode(\"\");\r\n            setPromotionDiscount(0);\r\n            setPromotionMessage(\"Promotion is no longer valid\");\r\n            setPromotionId(null);\r\n            sessionStorage.removeItem(\"promotionInfo\");\r\n          }, 0);\r\n        } finally {\r\n          setIsValidatingPromotion(false);\r\n        }\r\n      };\r\n\r\n      validatePromotion();\r\n    }, 100);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [dataRestored, subtotal, promotionCode, promotionId, promotionDiscount]); // Validate when subtotal changes or data is restored\r\n\r\n  // Handle navigation back to HomeDetailPage\r\n  const handleBackToHomeDetail = () => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      // Remove the current booking from stack\r\n      bookingStack.pop();\r\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n    }\r\n    // Don't clear promotion here - user might come back with same selection\r\n    // Promotion will be cleared only when new booking (different hotel/rooms) is detected\r\n    navigate(-1);\r\n  };\r\n\r\n  // Star rating component\r\n  const StarRating = ({ rating }) => {\r\n    return (\r\n      <div className=\"star-rating\">\r\n        {[...Array(5)].map((_, index) =>\r\n          index < rating ? (\r\n            <FaStar key={index} className=\"star filled\" />\r\n          ) : (\r\n            <FaRegStar key={index} className=\"star\" />\r\n          )\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\r\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\r\n  const [showPromotionErrorModal, setShowPromotionErrorModal] = useState(false);\r\n  const [promotionErrorMessage, setPromotionErrorMessage] = useState(\"\");\r\n  const [invalidPromotionCode, setInvalidPromotionCode] = useState(\"\");\r\n\r\n  // Add state for payment error modal\r\n  const [showPaymentErrorModal, setShowPaymentErrorModal] = useState(false);\r\n  const [paymentErrorMessage, setPaymentErrorMessage] = useState({\r\n    title: \"\",\r\n    mainMessage: \"\",\r\n    subMessage: \"\"\r\n  });\r\n\r\n  // Hàm xử lý áp dụng promotion từ modal với batch update để tránh multiple re-renders\r\n  const handleApplyPromotionFromModal = (promotionData) => {\r\n    // Batch update all promotion states at once to minimize re-renders\r\n    const updatePromotionStates = () => {\r\n      setPromotionCode(promotionData.code);\r\n      setPromotionDiscount(promotionData.discount);\r\n      setPromotionMessage(promotionData.message);\r\n      setPromotionId(promotionData.promotionId);\r\n    };\r\n\r\n    // Use setTimeout to batch the state updates\r\n    setTimeout(updatePromotionStates, 0);\r\n  };\r\n\r\n  // Function to validate promotion before booking (optimized to avoid unnecessary re-renders)\r\n  const validatePromotionBeforeBooking = async () => {\r\n    if (!promotionCode || !promotionId || promotionDiscount === 0) {\r\n      return { valid: true }; // No promotion to validate\r\n    }\r\n\r\n    // Only show loading state if validation takes longer than 300ms\r\n    let shouldShowLoading = false;\r\n    const loadingTimeoutId = setTimeout(() => {\r\n      shouldShowLoading = true;\r\n      setIsValidatingPromotionBeforeBooking(true);\r\n    }, 300);\r\n\r\n    try {\r\n      const res = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\r\n        code: promotionCode,\r\n        orderAmount: subtotal,\r\n      });\r\n\r\n      // Clear timeout and loading state\r\n      clearTimeout(loadingTimeoutId);\r\n      if (shouldShowLoading) {\r\n        setIsValidatingPromotionBeforeBooking(false);\r\n      }\r\n\r\n      if (!res.data.valid) {\r\n        return {\r\n          valid: false,\r\n          message: res.data.message || \"Promotion is no longer valid\",\r\n        };\r\n      }\r\n\r\n      if (res.data.discount !== promotionDiscount) {\r\n        return {\r\n          valid: false,\r\n          message:\r\n            \"Promotion discount has changed. Please reapply the promotion.\",\r\n        };\r\n      }\r\n\r\n      return { valid: true };\r\n    } catch (err) {\r\n      // Clear timeout and loading state\r\n      clearTimeout(loadingTimeoutId);\r\n      if (shouldShowLoading) {\r\n        setIsValidatingPromotionBeforeBooking(false);\r\n      }\r\n      return {\r\n        valid: false,\r\n        message: \"Unable to validate promotion. Please try again.\",\r\n      };\r\n    }\r\n  };\r\n\r\n  // Function to check hotel status before booking (optimized to avoid unnecessary loading states)\r\n  const checkHotelStatusBeforeBooking = async () => {\r\n    return new Promise((resolve, reject) => {\r\n      // Only show loading state if check takes longer than 300ms\r\n      let shouldShowLoading = false;\r\n      const loadingTimeoutId = setTimeout(() => {\r\n        shouldShowLoading = true;\r\n        setIsCheckingHotelStatus(true);\r\n      }, 300);\r\n\r\n      dispatch({\r\n        type: HotelActions.FETCH_DETAIL_HOTEL,\r\n        payload: {\r\n          hotelId: hotelDetail._id,\r\n          userId: Auth._id,\r\n          onSuccess: (hotel) => {\r\n            clearTimeout(loadingTimeoutId);\r\n            if (shouldShowLoading) {\r\n              setIsCheckingHotelStatus(false);\r\n            }\r\n            if (hotel.ownerStatus === \"ACTIVE\") {\r\n              resolve(hotel);\r\n            } else {\r\n              reject(new Error(\"Hotel is currently inactive\"));\r\n            }\r\n          },\r\n          onFailed: (error) => {\r\n            clearTimeout(loadingTimeoutId);\r\n            if (shouldShowLoading) {\r\n              setIsCheckingHotelStatus(false);\r\n            }\r\n            reject(new Error(error || \"Failed to check hotel status\"));\r\n          },\r\n          onError: () => {\r\n            clearTimeout(loadingTimeoutId);\r\n            if (shouldShowLoading) {\r\n              setIsCheckingHotelStatus(false);\r\n            }\r\n            reject(new Error(\"Server error while checking hotel status\"));\r\n          },\r\n        },\r\n      });\r\n    });\r\n  };\r\n  const createBooking = async () => {\r\n    try {\r\n      // Validate promotion first if there's one applied\r\n      const promotionValidation = await validatePromotionBeforeBooking();\r\n      if (!promotionValidation.valid) {\r\n        // Store error info for modal\r\n        setPromotionErrorMessage(promotionValidation.message);\r\n        setInvalidPromotionCode(promotionCode);\r\n\r\n        // Clear invalid promotion\r\n        setPromotionCode(\"\");\r\n        setPromotionDiscount(0);\r\n        setPromotionMessage(\"\");\r\n        setPromotionId(null);\r\n        sessionStorage.removeItem(\"promotionInfo\");\r\n\r\n        // Show error modal\r\n        setShowPromotionErrorModal(true);\r\n        return;\r\n      }\r\n\r\n      // Check hotel status\r\n      const hotel = await checkHotelStatusBeforeBooking();\r\n      console.log(\"Hotel detail fetched successfully:\", hotel);\r\n      const totalRoomPrice = selectedRooms.reduce(\r\n        (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n        0\r\n      );\r\n\r\n      const totalServicePrice = selectedServices.reduce((total, service) => {\r\n        const selectedDates = service.selectedDates || [];\r\n        const serviceQuantity = service.quantity * selectedDates.length;\r\n        return total + service.price * serviceQuantity;\r\n      }, 0);\r\n\r\n      const bookingSubtotal = totalRoomPrice + totalServicePrice;\r\n\r\n      const params = {\r\n        hotelId: hotelDetail._id,\r\n        checkOutDate: searchInfo.checkoutDate,\r\n        checkInDate: searchInfo.checkinDate,\r\n        totalPrice: bookingSubtotal, // giá gốc\r\n        finalPrice: finalPrice, // giá sau giảm giá\r\n        roomDetails: selectedRooms.map(({ room, amount }) => ({\r\n          room: {\r\n            _id: room._id,\r\n          },\r\n          amount: amount,\r\n        })),\r\n        serviceDetails: selectedServices.map((service) => ({\r\n          _id: service._id,\r\n          quantity: service.quantity * (service.selectedDates?.length || 0),\r\n          selectDate: service.selectedDates || [],\r\n        })),\r\n        // Thêm promotionId và promotionDiscount nếu có\r\n        ...(promotionId && { promotionId }),\r\n        ...(promotionDiscount > 0 && { promotionDiscount }),\r\n      };\r\n\r\n      console.log(\"params >> \", params);\r\n\r\n      // Helper function to save reservationId to bookingStack\r\n      const saveReservationIdToBookingStack = (reservationId) => {\r\n        if (reservationId) {\r\n          const bookingStack = JSON.parse(\r\n            sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n          );\r\n          if (bookingStack.length > 0) {\r\n            bookingStack[bookingStack.length - 1].reservationId = reservationId;\r\n            sessionStorage.setItem(\r\n              \"bookingStack\",\r\n              JSON.stringify(bookingStack)\r\n            );\r\n          }\r\n        }\r\n      };\r\n      try {\r\n        let reservationId = null;\r\n        const bookingStack = JSON.parse(\r\n          sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n        );\r\n        if (\r\n          bookingStack.length > 0 &&\r\n          bookingStack[bookingStack.length - 1].reservationId\r\n        ) {\r\n          reservationId = bookingStack[bookingStack.length - 1].reservationId;\r\n        }\r\n        const response = await Factories.create_booking({\r\n          ...params,\r\n          reservationId,\r\n        });\r\n        console.log(\"response >> \", response);\r\n        if (response?.status === 200) {\r\n          reservationId = response?.data?.unpaidReservation?._id;\r\n          saveReservationIdToBookingStack(reservationId);\r\n          const unpaidReservationId = reservationId;\r\n          const responseCheckout = await Factories.checkout_booking(\r\n            unpaidReservationId\r\n          );\r\n          console.log(\"responseCheckout >> \", responseCheckout);\r\n          const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n          if (paymentUrl) {\r\n            // Don't clear promotion here - user might come back from payment\r\n            // Promotion will be cleared when new booking is created\r\n            window.location.href = paymentUrl;\r\n          }\r\n        } else if (response?.status === 201) {\r\n          reservationId = response?.data?.reservation?._id;\r\n          saveReservationIdToBookingStack(reservationId);\r\n          const responseCheckout = await Factories.checkout_booking(\r\n            reservationId\r\n          );\r\n          const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n          if (paymentUrl) {\r\n            // Don't clear promotion here - user might come back from payment\r\n            // Promotion will be cleared when new booking is created\r\n            window.location.href = paymentUrl;\r\n          }\r\n        } else {\r\n          showToast.error(\"error create booking\");\r\n        }\r\n      } catch (error) {\r\n        \r\n        console.error(\"Error create payment: \", error.response?.data?.message);\r\n        setPaymentErrorMessage({\r\n          title: \"Payment Error\",\r\n          mainMessage: \"Unable to process your payment at this time\",\r\n          subMessage: error.response?.data?.message || \"Please try again later\"\r\n        });\r\n        setShowPaymentErrorModal(true);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error checking hotel status:\", error);\r\n      setShowModalStatusBooking(true);\r\n    }\r\n  };\r\n\r\n  const handleAccept = async () => {\r\n    const totalRoomPrice = selectedRooms.reduce(\r\n      (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n      0\r\n    );\r\n\r\n    if (totalRoomPrice > 0) {\r\n      // Final validation before creating booking\r\n      await createBooking();\r\n\r\n      // Only clear selection if booking was successful\r\n      // (createBooking will handle errors and not reach this point if failed)\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: [],\r\n          selectedServices: [],\r\n          hotelDetail: hotelDetail,\r\n        },\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleConfirmBooking = () => {\r\n    setShowAcceptModal(true);\r\n  };\r\n\r\n  // Only show loading spinner during initial load, not during re-renders\r\n  if (isInitialLoading || (!hotelDetail && !dataRestored)) {\r\n    return (\r\n      <div\r\n        className=\"d-flex justify-content-center align-items-center\"\r\n        style={{ height: \"100vh\" }}\r\n      >\r\n        <div className=\"spinner-border text-primary\" role=\"status\">\r\n          <span className=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // If data is restored but hotelDetail is still missing, redirect back\r\n  if (!hotelDetail && dataRestored) {\r\n    navigate(-1);\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"d-flex flex-column min-vh-100\"\r\n      style={{\r\n        backgroundImage: `url(${Banner})`,\r\n        backgroundSize: \"cover\",\r\n        backgroundPosition: \"center\",\r\n      }}\r\n    >\r\n      <Header />\r\n      <div\r\n        className=\"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\"\r\n        style={{ paddingTop: \"65px\", paddingBottom: \"50px\" }}\r\n      >\r\n        <Container className=\"mt-4\">\r\n          <Row className=\"justify-content-center\">\r\n            <ToastProvider />\r\n            {/* Left Card - Booking Details */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"booking-card text-white\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  marginBottom: \"20px\",\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"stars mb-2\"\r\n                  style={{\r\n                    justifyContent: \"flex-start\",\r\n                    justifyItems: \"self-start\",\r\n                  }}\r\n                >\r\n                  <StarRating rating={hotelDetail.star} />\r\n                </div>\r\n\r\n                <h4 className=\"hotel-name mb-1\">\r\n                  {hotelDetail.hotelName ?? \"\"}\r\n                </h4>\r\n\r\n                <p className=\"hotel-address small mb-4\">\r\n                  {hotelDetail.address ?? \"\"}\r\n                </p>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <h5 className=\"mb-4\">Your booking detail</h5>\r\n\r\n                <Row className=\"mb-4\">\r\n                  <Col xs={6}>\r\n                    <div className=\"checkin\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkin\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkinDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                  <Col xs={6}>\r\n                    <div className=\"checkout\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkout\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkoutDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                </Row>\r\n\r\n                <div className=\"stay-info mb-2\">\r\n                  <div className=\"d-flex justify-content-between mb-2\">\r\n                    <span>Total length of stay:</span>\r\n                    <span className=\"fw-bold\">{numberOfDays} night</span>{\" \"}\r\n                  </div>\r\n                  <div className=\"d-flex justify-content-between mb-3\">\r\n                    <span>Total number of people:</span>\r\n                    <span className=\"fw-bold\">\r\n                      {searchInfo.adults} Adults - {searchInfo.childrens}{\" \"}\r\n                      Childrens\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"selected-room mb-2\">\r\n                  <h5 className=\"mb-4\">You selected</h5>\r\n\r\n                  {selectedRooms.map(({ room, amount }) => (\r\n                    <div\r\n                      key={room._id}\r\n                      className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                    >\r\n                      <span>\r\n                        {amount} x {room.name} ({numberOfDays} days):\r\n                      </span>\r\n                      <span className=\"fw-bold\">\r\n                        {Utils.formatCurrency(\r\n                          room.price * amount * numberOfDays\r\n                        )}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n\r\n                  <div className=\"small mb-3\">\r\n                    <a\r\n                      className=\"text-blue text-decoration-none\"\r\n                      style={{ cursor: \"pointer\" }}\r\n                      onClick={handleBackToHomeDetail}\r\n                    >\r\n                      Change your selection\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Selected Services Section */}\r\n                {selectedServices.length > 0 && (\r\n                  <div className=\"selected-services mb-2\">\r\n                    <h5 className=\"mb-3\">Selected Services</h5>\r\n\r\n                    {selectedServices.map((service) => {\r\n                      const selectedDates = service.selectedDates || [];\r\n                      const serviceQuantity =\r\n                        service.quantity * selectedDates.length;\r\n                      const serviceTotal = service.price * serviceQuantity;\r\n\r\n                      return (\r\n                        <div\r\n                          key={service._id}\r\n                          className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                        >\r\n                          <span>\r\n                            {service.quantity} x {service.name} (\r\n                            {selectedDates.length} days):\r\n                          </span>\r\n                          <span className=\"fw-bold\">\r\n                            {Utils.formatCurrency(serviceTotal)}\r\n                          </span>\r\n                        </div>\r\n                      );\r\n                    })}\r\n\r\n                    <div className=\"small mb-3\">\r\n                      <a\r\n                        className=\"text-blue text-decoration-none\"\r\n                        style={{ cursor: \"pointer\" }}\r\n                        onClick={() => {\r\n                          dispatch({\r\n                            type: SearchActions.SAVE_SELECTED_ROOMS,\r\n                            payload: {\r\n                              selectedRooms: selectedRooms,\r\n                              selectedServices: selectedServices,\r\n                              hotelDetail: hotelDetail,\r\n                            },\r\n                          });\r\n                          navigate(-1);\r\n                        }}\r\n                      >\r\n                        Change your selection\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"promotion-section mb-3\">\r\n                  {/* Current applied promotion display */}\r\n                  {promotionDiscount > 0 ? (\r\n                    <Card\r\n                      className=\"promotion-applied mb-3\"\r\n                      style={{\r\n                        backgroundColor: \"rgba(40, 167, 69, 0.2)\",\r\n                        borderColor: \"#28a745\",\r\n                        border: \"2px solid #28a745\",\r\n                      }}\r\n                    >\r\n                      <Card.Body className=\"py-2\">\r\n                        <div className=\"d-flex justify-content-between align-items-center\">\r\n                          <div>\r\n                            <div className=\"d-flex align-items-center\">\r\n                              <FaTag className=\"text-success me-2\" />\r\n                              <span className=\"fw-bold text-success\">\r\n                                {promotionCode}\r\n                              </span>\r\n                            </div>\r\n                            <small className=\"text-success\">\r\n                              Save {Utils.formatCurrency(promotionDiscount)}\r\n                            </small>\r\n                          </div>\r\n                          <Button\r\n                            variant=\"outline-danger\"\r\n                            size=\"sm\"\r\n                            onClick={() =>\r\n                              handleApplyPromotionFromModal({\r\n                                code: \"\",\r\n                                discount: 0,\r\n                                message: \"\",\r\n                                promotionId: null,\r\n                              })\r\n                            }\r\n                            className=\"d-flex align-items-center\"\r\n                            disabled={\r\n                              isValidatingPromotion ||\r\n                              isValidatingPromotionBeforeBooking\r\n                            }\r\n                          >\r\n                            <FaTimes className=\"me-1\" />\r\n                            {isValidatingPromotion ||\r\n                            isValidatingPromotionBeforeBooking\r\n                              ? \"...\"\r\n                              : \"Remove\"}\r\n                          </Button>\r\n                        </div>\r\n                      </Card.Body>\r\n                    </Card>\r\n                  ) : (\r\n                    <div\r\n                      className=\"text-center py-3 mb-3\"\r\n                      style={{\r\n                        border: \"2px dashed rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"8px\",\r\n                        backgroundColor: \"rgba(255,255,255,0.05)\",\r\n                      }}\r\n                    >\r\n                      <FaTag className=\"text-muted mb-2\" size={24} />\r\n                      <div className=\"text-muted small\">\r\n                        No promotion applied\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Button to open promotion modal */}\r\n                  <Button\r\n                    variant=\"outline-light\"\r\n                    className=\"w-100 d-flex align-items-center justify-content-center\"\r\n                    onClick={() => setShowPromotionModal(true)}\r\n                    style={{\r\n                      borderStyle: \"dashed\",\r\n                      borderWidth: \"2px\",\r\n                      padding: \"12px\",\r\n                    }}\r\n                    disabled={\r\n                      isValidatingPromotion ||\r\n                      isValidatingPromotionBeforeBooking\r\n                    }\r\n                  >\r\n                    <FaTag className=\"me-2\" />\r\n                    {isValidatingPromotion || isValidatingPromotionBeforeBooking\r\n                      ? \"Validating...\"\r\n                      : promotionDiscount > 0\r\n                      ? \"Change Promotion\"\r\n                      : \"Select Promotion\"}\r\n                  </Button>\r\n\r\n                  {/* Validation status indicator */}\r\n                  {(isValidatingPromotion ||\r\n                    isValidatingPromotionBeforeBooking) && (\r\n                    <div className=\"text-center mt-2\">\r\n                      <small className=\"text-info\">\r\n                        <div\r\n                          className=\"spinner-border spinner-border-sm me-1\"\r\n                          role=\"status\"\r\n                        >\r\n                          <span className=\"visually-hidden\">Loading...</span>\r\n                        </div>\r\n                        Checking promotion validity...\r\n                      </small>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Price breakdown section */}\r\n                <div className=\"price-breakdown\">\r\n                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                    <span>Subtotal:</span>\r\n                    <span className=\"fw-bold\">\r\n                      {Utils.formatCurrency(subtotal)}\r\n                    </span>\r\n                  </div>\r\n\r\n                  {promotionDiscount > 0 && (\r\n                    <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                      <span className=\"text-success\">Discount:</span>\r\n                      <span className=\"fw-bold text-success\">\r\n                        -{Utils.formatCurrency(promotionDiscount)}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div\r\n                    className=\"booking-divider mb-2\"\r\n                    style={{\r\n                      height: \"1px\",\r\n                      backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                      margin: \"10px 0\",\r\n                    }}\r\n                  ></div>\r\n\r\n                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                    <h5 className=\"text-danger mb-0\">\r\n                      Total: {Utils.formatCurrency(finalPrice)}\r\n                    </h5>\r\n                  </div>\r\n                  <div className=\"small\">Includes taxes and fees</div>\r\n                </div>\r\n              </Card>\r\n            </Col>\r\n\r\n            {/* Right Card - Customer Information */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"info-card\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  color: \"white\",\r\n                }}\r\n              >\r\n                <h4 className=\"mb-4\">Check your information</h4>\r\n\r\n                <Form>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Full name</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={Auth.name}\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Email</Form.Label>\r\n                    <Form.Control\r\n                      type=\"email\"\r\n                      value={Auth.email}\r\n                      placeholder=\"<EMAIL>\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Phone</Form.Label>\r\n                    <Form.Control\r\n                      type=\"tel\"\r\n                      value={Auth.phoneNumber}\r\n                      placeholder=\"0912345678\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Who are you booking for?</Form.Label>\r\n                    <div>\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"mainGuest\"\r\n                        label=\"I'm the main guest\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"mainGuest\"}\r\n                        onChange={() => setBookingFor(\"mainGuest\")}\r\n                        className=\"mb-2\"\r\n                      />\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"someoneElse\"\r\n                        label=\"I'm booking for someone else\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"someoneElse\"}\r\n                        onChange={() => setBookingFor(\"someoneElse\")}\r\n                      />\r\n                    </div>\r\n                  </Form.Group>\r\n\r\n                  <div className=\"text-center\">\r\n                    <Button\r\n                      className=\"px-4 py-2\"\r\n                      style={{\r\n                        borderRadius: \"10px\",\r\n                        backgroundColor: \"white\",\r\n                        color: \"#007bff\",\r\n                        border: \"none\",\r\n                        fontWeight: \"bold\",\r\n                      }}\r\n                      onClick={handleConfirmBooking}\r\n                      disabled={\r\n                        isCheckingHotelStatus ||\r\n                        isValidatingPromotion ||\r\n                        isValidatingPromotionBeforeBooking\r\n                      }\r\n                    >\r\n                      {isValidatingPromotionBeforeBooking\r\n                        ? \"Validating Promotion...\"\r\n                        : isCheckingHotelStatus\r\n                        ? \"Checking Hotel...\"\r\n                        : \"Booking\"}\r\n                    </Button>\r\n                    {/* Accept Confirmation Modal */}\r\n                    <ConfirmationModal\r\n                      show={showAcceptModal}\r\n                      onHide={() => setShowAcceptModal(false)}\r\n                      onConfirm={handleAccept}\r\n                      title=\"Confirm Acceptance\"\r\n                      message=\"Do you want to proceed with this booking confirmation?\"\r\n                      confirmButtonText=\"Accept\"\r\n                      type=\"accept\"\r\n                    />\r\n                  </div>\r\n                </Form>\r\n              </Card>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n        <div>\r\n          <ChatBox />\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n\r\n      {/* Promotion Modal */}\r\n      <PromotionModal\r\n        show={showPromotionModal}\r\n        onHide={() => setShowPromotionModal(false)}\r\n        totalPrice={subtotal}\r\n        onApplyPromotion={handleApplyPromotionFromModal}\r\n        currentPromotionId={promotionId}\r\n      />\r\n\r\n      <HotelClosedModal\r\n        show={showModalStatusBooking}\r\n        onClose={() => {\r\n          setShowModalStatusBooking(false);\r\n        }}\r\n      />\r\n\r\n      {/* Promotion Error Modal */}\r\n      <PromotionErrorModal\r\n        show={showPromotionErrorModal}\r\n        onClose={() => {\r\n          setShowPromotionErrorModal(false);\r\n          setPromotionErrorMessage(\"\");\r\n          setInvalidPromotionCode(\"\");\r\n        }}\r\n        onSelectNewPromotion={() => {\r\n          setShowPromotionErrorModal(false);\r\n          setPromotionErrorMessage(\"\");\r\n          setInvalidPromotionCode(\"\");\r\n          setShowPromotionModal(true);\r\n        }}\r\n        errorMessage={promotionErrorMessage}\r\n        promotionCode={invalidPromotionCode}\r\n      />\r\n\r\n      <RoomClosedModal \r\n        show={showPaymentErrorModal}\r\n        onClose={() => setShowPaymentErrorModal(false)}\r\n        title={paymentErrorMessage.title}\r\n        mainMessage={paymentErrorMessage.mainMessage}\r\n        subMessage={paymentErrorMessage.subMessage}\r\n        buttonText=\"Try Again\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BookingCheckPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,OAAO;AACvD,SACEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,UAAU,QACL,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,QAAQ,gBAAgB;AAClE,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,SAASC,cAAc,EAAEC,cAAc,QAAQ,sBAAsB;AACrE,SAASC,cAAc,QAAQ,kCAAkC;AACjE,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,SAAS,MAAM,iCAAiC;AACvD,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,SAASC,SAAS,EAAEC,aAAa,QAAQ,4BAA4B;AACrE,OAAOC,gBAAgB,MAAM,kBAAkB;AAC/C,OAAOC,eAAe,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA;EAC7B,MAAMC,YAAY,GAAGR,gBAAgB,CAAC,CAAC,CAAC,CAAC;;EAEzC,MAAM,CAACS,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAE7C,QAAQ,CAAC,IAAI,CAAC;EACvC,MAAM8C,IAAI,GAAGxB,cAAc,CAAEyB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAACA,IAAI,CAAC;EACvD,MAAME,iBAAiB,GAAG1B,cAAc,CACrCyB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACD,iBAC1B,CAAC;EAED,MAAME,kBAAkB,GAAG5B,cAAc,CACtCyB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACE,aAC1B,CAAC;EACD,MAAMC,yBAAyB,GAAG9B,cAAc,CAC7CyB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACI,gBAC1B,CAAC;EACD,MAAMC,oBAAoB,GAAGhC,cAAc,CACxCyB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACM,WAC1B,CAAC;EACD,MAAMC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,QAAQ,GAAGlC,cAAc,CAAC,CAAC;EACjC,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,WAAW,CAAC;;EAEzD;EACA,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACgE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkE,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAC;IAC7CmD,aAAa,EAAED,kBAAkB,IAAI,EAAE;IACvCG,gBAAgB,EAAED,yBAAyB,IAAI,EAAE;IACjDG,WAAW,EAAED,oBAAoB,IAAI,IAAI;IACzCgB,UAAU,EAAEtB;EACd,CAAC,CAAC;EAEF,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC2E,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CACJ6E,kCAAkC,EAClCC,qCAAqC,CACtC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACnB,MAAM,CAAC+E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMgF,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,cAAc,GAAGN,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC;MAC5DjB,cAAc,CAACkB,cAAc,CAAC;;MAE9B;MACA9B,QAAQ,CAAC;QACP+B,IAAI,EAAE5D,aAAa,CAAC6D,mBAAmB;QACvCC,OAAO,EAAE;UACPvC,aAAa,EAAEoC,cAAc,CAACpC,aAAa;UAC3CE,gBAAgB,EAAEkC,cAAc,CAAClC,gBAAgB;UACjDE,WAAW,EAAEgC,cAAc,CAAChC;QAC9B;MACF,CAAC,CAAC;IACJ;IACAiB,eAAe,CAAC,IAAI,CAAC;IACrBQ,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC,EAAE,CAACvB,QAAQ,CAAC,CAAC;;EAEd;EACAxD,SAAS,CAAC,MAAM;IACd,IAAIsE,YAAY,EAAE;MAChB,MAAMoB,KAAK,GAAGT,IAAI,CAACC,KAAK,CACtBC,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,MAC7C,CAAC;MACD,IAAIM,KAAK,EAAE;QAAA,IAAAC,qBAAA,EAAAC,qBAAA;QACT;QACA,MAAMC,cAAc,IAAAF,qBAAA,GAAGxB,WAAW,CAACb,WAAW,cAAAqC,qBAAA,uBAAvBA,qBAAA,CAAyBG,GAAG;QACnD,MAAMC,YAAY,GAAGL,KAAK,CAACM,OAAO;QAClC,MAAMC,gBAAgB,GAAGhB,IAAI,CAACiB,SAAS,EAAAN,qBAAA,GACrCzB,WAAW,CAACjB,aAAa,cAAA0C,qBAAA,uBAAzBA,qBAAA,CACIO,GAAG,CAAEC,CAAC,KAAM;UAAEC,MAAM,EAAED,CAAC,CAACE,IAAI,CAACR,GAAG;UAAES,MAAM,EAAEH,CAAC,CAACG;QAAO,CAAC,CAAC,CAAC,CACvDC,IAAI,CAAC,CACV,CAAC;QACD,MAAMC,cAAc,GAAGf,KAAK,CAACgB,SAAS;QAEtC,IACEb,cAAc,KAAKE,YAAY,IAC/BE,gBAAgB,KAAKQ,cAAc,EACnC;UACA;UACAtB,cAAc,CAACwB,UAAU,CAAC,eAAe,CAAC;UAC1CC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;UAC7D;QACF;;QAEA;QACA,MAAMC,SAAS,GAAGpB,KAAK,CAACoB,SAAS,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC;QAC/C,MAAMC,QAAQ,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;QACvC,MAAMI,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;QAEjC,IAAID,QAAQ,GAAGC,WAAW,EAAE;UAC1B;UACAN,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UACnDjD,gBAAgB,CAAC8B,KAAK,CAAC/B,aAAa,IAAI,EAAE,CAAC;UAC3CG,oBAAoB,CAAC4B,KAAK,CAAC7B,iBAAiB,IAAI,CAAC,CAAC;UAClDG,mBAAmB,CAAC,yBAAyB,CAAC;UAC9CE,cAAc,CAACwB,KAAK,CAACzB,WAAW,IAAI,IAAI,CAAC;QAC3C,CAAC,MAAM;UACLL,gBAAgB,CAAC8B,KAAK,CAAC/B,aAAa,IAAI,EAAE,CAAC;UAC3CG,oBAAoB,CAAC4B,KAAK,CAAC7B,iBAAiB,IAAI,CAAC,CAAC;UAClDG,mBAAmB,CAAC0B,KAAK,CAAC3B,gBAAgB,IAAI,EAAE,CAAC;UACjDG,cAAc,CAACwB,KAAK,CAACzB,WAAW,IAAI,IAAI,CAAC;UACzC2C,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzCnB,KAAK,CAAC/B,aACR,CAAC;QACH;MACF;IACF;EACF,CAAC,EAAE,CAACW,YAAY,EAAEH,WAAW,CAACb,WAAW,EAAEa,WAAW,CAACjB,aAAa,CAAC,CAAC;;EAEtE;EACAlD,SAAS,CAAC,MAAM;IACd,IAAIsE,YAAY,EAAE;MAAA,IAAA6C,sBAAA,EAAAC,sBAAA;MAChB;MACAjC,cAAc,CAACkC,OAAO,CACpB,eAAe,EACfpC,IAAI,CAACiB,SAAS,CAAC;QACbvC,aAAa;QACbE,iBAAiB;QACjBE,gBAAgB;QAChBE,WAAW;QACX6C,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QAAE;QACvB;QACAhB,OAAO,GAAAmB,sBAAA,GAAEhD,WAAW,CAACb,WAAW,cAAA6D,sBAAA,uBAAvBA,sBAAA,CAAyBrB,GAAG;QACrCY,SAAS,EAAEzB,IAAI,CAACiB,SAAS,EAAAkB,sBAAA,GACvBjD,WAAW,CAACjB,aAAa,cAAAkE,sBAAA,uBAAzBA,sBAAA,CACIjB,GAAG,CAAEC,CAAC,KAAM;UAAEC,MAAM,EAAED,CAAC,CAACE,IAAI,CAACR,GAAG;UAAES,MAAM,EAAEH,CAAC,CAACG;QAAO,CAAC,CAAC,CAAC,CACvDC,IAAI,CAAC,CACV;MACF,CAAC,CACH,CAAC;IACH;EACF,CAAC,EAAE,CACD7C,aAAa,EACbE,iBAAiB,EACjBE,gBAAgB,EAChBE,WAAW,EACXK,YAAY,EACZH,WAAW,CAACb,WAAW,EACvBa,WAAW,CAACjB,aAAa,CAC1B,CAAC;;EAEF;EACA,MAAMA,aAAa,GAAGiB,WAAW,CAACjB,aAAa;EAC/C,MAAME,gBAAgB,GAAGe,WAAW,CAACf,gBAAgB;EACrD,MAAME,WAAW,GAAGa,WAAW,CAACb,WAAW;EAC3C,MAAMe,UAAU,GAAGF,WAAW,CAACE,UAAU;;EAEzC;EACA,MAAMiD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAG,IAAIR,IAAI,CAAC1C,UAAU,CAACmD,WAAW,CAAC;IAChD,MAAMC,QAAQ,GAAG,IAAIV,IAAI,CAAC1C,UAAU,CAACqD,YAAY,CAAC;IAClD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAGF,OAAO,CAAC;IAC7C,MAAMO,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOG,QAAQ;EACjB,CAAC;EAED,MAAME,YAAY,GAAGV,qBAAqB,CAAC,CAAC;;EAE5C;EACA,MAAMW,cAAc,GAAG/E,aAAa,CAACgF,MAAM,CACzC,CAACC,KAAK,EAAE;IAAE7B,IAAI;IAAEC;EAAO,CAAC,KAAK4B,KAAK,GAAG7B,IAAI,CAAC8B,KAAK,GAAG7B,MAAM,GAAGyB,YAAY,EACvE,CACF,CAAC;EACD,MAAMK,iBAAiB,GAAGjF,gBAAgB,CAAC8E,MAAM,CAAC,CAACC,KAAK,EAAEG,OAAO,KAAK;IACpE,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;IACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAClD,MAAM;IAC/D,OAAO8C,KAAK,GAAGG,OAAO,CAACF,KAAK,GAAGI,eAAe;EAChD,CAAC,EAAE,CAAC,CAAC;EACL,MAAME,QAAQ,GAAGT,cAAc,GAAGI,iBAAiB;EACnD,MAAMM,UAAU,GAAGf,IAAI,CAACgB,GAAG,CAACF,QAAQ,GAAG7E,iBAAiB,EAAE,CAAC,CAAC;;EAE5D;EACA7D,SAAS,CAAC,MAAM;IACd,IACE,CAACsE,YAAY,IACb,CAACX,aAAa,IACd,CAACM,WAAW,IACZJ,iBAAiB,KAAK,CAAC,EAEvB;;IAEF;IACA,MAAMgF,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjC,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpC;QACA,MAAMC,gBAAgB,GAAGF,UAAU,CAAC,MAAM;UACxCrE,wBAAwB,CAAC,IAAI,CAAC;QAChC,CAAC,EAAE,GAAG,CAAC;QAEP,IAAI;UACF,MAAMwE,GAAG,GAAG,MAAMC,KAAK,CAACC,IAAI,CAAC,GAAG3G,YAAY,uBAAuB,EAAE;YACnE4G,IAAI,EAAEzF,aAAa;YACnB0F,WAAW,EAAEX;UACf,CAAC,CAAC;UAEFY,YAAY,CAACN,gBAAgB,CAAC;UAE9B,IAAI,CAACC,GAAG,CAACM,IAAI,CAACC,KAAK,IAAIP,GAAG,CAACM,IAAI,CAACE,QAAQ,KAAK5F,iBAAiB,EAAE;YAC9D;YACAiF,UAAU,CAAC,MAAM;cACflF,gBAAgB,CAAC,EAAE,CAAC;cACpBE,oBAAoB,CAAC,CAAC,CAAC;cACvBE,mBAAmB,CACjB,qDACF,CAAC;cACDE,cAAc,CAAC,IAAI,CAAC;cACpBiB,cAAc,CAACwB,UAAU,CAAC,eAAe,CAAC;YAC5C,CAAC,EAAE,CAAC,CAAC;UACP;QACF,CAAC,CAAC,OAAO+C,GAAG,EAAE;UACZJ,YAAY,CAACN,gBAAgB,CAAC;UAC9B;UACAF,UAAU,CAAC,MAAM;YACflF,gBAAgB,CAAC,EAAE,CAAC;YACpBE,oBAAoB,CAAC,CAAC,CAAC;YACvBE,mBAAmB,CAAC,8BAA8B,CAAC;YACnDE,cAAc,CAAC,IAAI,CAAC;YACpBiB,cAAc,CAACwB,UAAU,CAAC,eAAe,CAAC;UAC5C,CAAC,EAAE,CAAC,CAAC;QACP,CAAC,SAAS;UACRlC,wBAAwB,CAAC,KAAK,CAAC;QACjC;MACF,CAAC;MAEDsE,iBAAiB,CAAC,CAAC;IACrB,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMO,YAAY,CAACT,SAAS,CAAC;EACtC,CAAC,EAAE,CAACvE,YAAY,EAAEoE,QAAQ,EAAE/E,aAAa,EAAEM,WAAW,EAAEJ,iBAAiB,CAAC,CAAC,CAAC,CAAC;;EAE7E;EACA,MAAM8F,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAM3E,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B;MACAL,YAAY,CAAC4E,GAAG,CAAC,CAAC;MAClBzE,cAAc,CAACkC,OAAO,CAAC,cAAc,EAAEpC,IAAI,CAACiB,SAAS,CAAClB,YAAY,CAAC,CAAC;IACtE;IACA;IACA;IACAzB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMsG,UAAU,GAAGA,CAAC;IAAEC;EAAO,CAAC,KAAK;IACjC,oBACE3H,OAAA;MAAK4H,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC9D,GAAG,CAAC,CAAC+D,CAAC,EAAEC,KAAK,KAC1BA,KAAK,GAAGL,MAAM,gBACZ3H,OAAA,CAAC1B,MAAM;QAAasJ,SAAS,EAAC;MAAa,GAA9BI,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA2B,CAAC,gBAE9CpI,OAAA,CAACzB,SAAS;QAAaqJ,SAAS,EAAC;MAAM,GAAvBI,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAE7C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1K,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2K,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5K,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC6K,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG9K,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAAC+K,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGhL,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACiL,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlL,QAAQ,CAAC,EAAE,CAAC;;EAEpE;EACA,MAAM,CAACmL,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpL,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACqL,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtL,QAAQ,CAAC;IAC7DuL,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAMC,6BAA6B,GAAIC,aAAa,IAAK;IACvD;IACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;MAClC/H,gBAAgB,CAAC8H,aAAa,CAACtC,IAAI,CAAC;MACpCtF,oBAAoB,CAAC4H,aAAa,CAACjC,QAAQ,CAAC;MAC5CzF,mBAAmB,CAAC0H,aAAa,CAACE,OAAO,CAAC;MAC1C1H,cAAc,CAACwH,aAAa,CAACzH,WAAW,CAAC;IAC3C,CAAC;;IAED;IACA6E,UAAU,CAAC6C,qBAAqB,EAAE,CAAC,CAAC;EACtC,CAAC;;EAED;EACA,MAAME,8BAA8B,GAAG,MAAAA,CAAA,KAAY;IACjD,IAAI,CAAClI,aAAa,IAAI,CAACM,WAAW,IAAIJ,iBAAiB,KAAK,CAAC,EAAE;MAC7D,OAAO;QAAE2F,KAAK,EAAE;MAAK,CAAC,CAAC,CAAC;IAC1B;;IAEA;IACA,IAAIsC,iBAAiB,GAAG,KAAK;IAC7B,MAAM9C,gBAAgB,GAAGF,UAAU,CAAC,MAAM;MACxCgD,iBAAiB,GAAG,IAAI;MACxBjH,qCAAqC,CAAC,IAAI,CAAC;IAC7C,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI;MACF,MAAMoE,GAAG,GAAG,MAAMC,KAAK,CAACC,IAAI,CAAC,GAAG3G,YAAY,uBAAuB,EAAE;QACnE4G,IAAI,EAAEzF,aAAa;QACnB0F,WAAW,EAAEX;MACf,CAAC,CAAC;;MAEF;MACAY,YAAY,CAACN,gBAAgB,CAAC;MAC9B,IAAI8C,iBAAiB,EAAE;QACrBjH,qCAAqC,CAAC,KAAK,CAAC;MAC9C;MAEA,IAAI,CAACoE,GAAG,CAACM,IAAI,CAACC,KAAK,EAAE;QACnB,OAAO;UACLA,KAAK,EAAE,KAAK;UACZoC,OAAO,EAAE3C,GAAG,CAACM,IAAI,CAACqC,OAAO,IAAI;QAC/B,CAAC;MACH;MAEA,IAAI3C,GAAG,CAACM,IAAI,CAACE,QAAQ,KAAK5F,iBAAiB,EAAE;QAC3C,OAAO;UACL2F,KAAK,EAAE,KAAK;UACZoC,OAAO,EACL;QACJ,CAAC;MACH;MAEA,OAAO;QAAEpC,KAAK,EAAE;MAAK,CAAC;IACxB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZ;MACAJ,YAAY,CAACN,gBAAgB,CAAC;MAC9B,IAAI8C,iBAAiB,EAAE;QACrBjH,qCAAqC,CAAC,KAAK,CAAC;MAC9C;MACA,OAAO;QACL2E,KAAK,EAAE,KAAK;QACZoC,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAMG,6BAA6B,GAAG,MAAAA,CAAA,KAAY;IAChD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC;MACA,IAAIJ,iBAAiB,GAAG,KAAK;MAC7B,MAAM9C,gBAAgB,GAAGF,UAAU,CAAC,MAAM;QACxCgD,iBAAiB,GAAG,IAAI;QACxBnH,wBAAwB,CAAC,IAAI,CAAC;MAChC,CAAC,EAAE,GAAG,CAAC;MAEPnB,QAAQ,CAAC;QACP+B,IAAI,EAAE3D,YAAY,CAACuK,kBAAkB;QACrC1G,OAAO,EAAE;UACPO,OAAO,EAAE1C,WAAW,CAACwC,GAAG;UACxBsG,MAAM,EAAEvJ,IAAI,CAACiD,GAAG;UAChBuG,SAAS,EAAGC,KAAK,IAAK;YACpBhD,YAAY,CAACN,gBAAgB,CAAC;YAC9B,IAAI8C,iBAAiB,EAAE;cACrBnH,wBAAwB,CAAC,KAAK,CAAC;YACjC;YACA,IAAI2H,KAAK,CAACC,WAAW,KAAK,QAAQ,EAAE;cAClCN,OAAO,CAACK,KAAK,CAAC;YAChB,CAAC,MAAM;cACLJ,MAAM,CAAC,IAAIM,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAClD;UACF,CAAC;UACDC,QAAQ,EAAG9J,KAAK,IAAK;YACnB2G,YAAY,CAACN,gBAAgB,CAAC;YAC9B,IAAI8C,iBAAiB,EAAE;cACrBnH,wBAAwB,CAAC,KAAK,CAAC;YACjC;YACAuH,MAAM,CAAC,IAAIM,KAAK,CAAC7J,KAAK,IAAI,8BAA8B,CAAC,CAAC;UAC5D,CAAC;UACD+J,OAAO,EAAEA,CAAA,KAAM;YACbpD,YAAY,CAACN,gBAAgB,CAAC;YAC9B,IAAI8C,iBAAiB,EAAE;cACrBnH,wBAAwB,CAAC,KAAK,CAAC;YACjC;YACAuH,MAAM,CAAC,IAAIM,KAAK,CAAC,0CAA0C,CAAC,CAAC;UAC/D;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,MAAMG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF;MACA,MAAMC,mBAAmB,GAAG,MAAMf,8BAA8B,CAAC,CAAC;MAClE,IAAI,CAACe,mBAAmB,CAACpD,KAAK,EAAE;QAC9B;QACAuB,wBAAwB,CAAC6B,mBAAmB,CAAChB,OAAO,CAAC;QACrDX,uBAAuB,CAACtH,aAAa,CAAC;;QAEtC;QACAC,gBAAgB,CAAC,EAAE,CAAC;QACpBE,oBAAoB,CAAC,CAAC,CAAC;QACvBE,mBAAmB,CAAC,EAAE,CAAC;QACvBE,cAAc,CAAC,IAAI,CAAC;QACpBiB,cAAc,CAACwB,UAAU,CAAC,eAAe,CAAC;;QAE1C;QACAkE,0BAA0B,CAAC,IAAI,CAAC;QAChC;MACF;;MAEA;MACA,MAAMyB,KAAK,GAAG,MAAMP,6BAA6B,CAAC,CAAC;MACnDnF,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEyF,KAAK,CAAC;MACxD,MAAMrE,cAAc,GAAG/E,aAAa,CAACgF,MAAM,CACzC,CAACC,KAAK,EAAE;QAAE7B,IAAI;QAAEC;MAAO,CAAC,KAAK4B,KAAK,GAAG7B,IAAI,CAAC8B,KAAK,GAAG7B,MAAM,GAAGyB,YAAY,EACvE,CACF,CAAC;MAED,MAAMK,iBAAiB,GAAGjF,gBAAgB,CAAC8E,MAAM,CAAC,CAACC,KAAK,EAAEG,OAAO,KAAK;QACpE,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;QACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAClD,MAAM;QAC/D,OAAO8C,KAAK,GAAGG,OAAO,CAACF,KAAK,GAAGI,eAAe;MAChD,CAAC,EAAE,CAAC,CAAC;MAEL,MAAMqE,eAAe,GAAG5E,cAAc,GAAGI,iBAAiB;MAE1D,MAAMyE,MAAM,GAAG;QACb9G,OAAO,EAAE1C,WAAW,CAACwC,GAAG;QACxBiH,YAAY,EAAE1I,UAAU,CAACqD,YAAY;QACrCsF,WAAW,EAAE3I,UAAU,CAACmD,WAAW;QACnCyF,UAAU,EAAEJ,eAAe;QAAE;QAC7BlE,UAAU,EAAEA,UAAU;QAAE;QACxBuE,WAAW,EAAEhK,aAAa,CAACiD,GAAG,CAAC,CAAC;UAAEG,IAAI;UAAEC;QAAO,CAAC,MAAM;UACpDD,IAAI,EAAE;YACJR,GAAG,EAAEQ,IAAI,CAACR;UACZ,CAAC;UACDS,MAAM,EAAEA;QACV,CAAC,CAAC,CAAC;QACH4G,cAAc,EAAE/J,gBAAgB,CAAC+C,GAAG,CAAEmC,OAAO;UAAA,IAAA8E,qBAAA;UAAA,OAAM;YACjDtH,GAAG,EAAEwC,OAAO,CAACxC,GAAG;YAChB2C,QAAQ,EAAEH,OAAO,CAACG,QAAQ,IAAI,EAAA2E,qBAAA,GAAA9E,OAAO,CAACC,aAAa,cAAA6E,qBAAA,uBAArBA,qBAAA,CAAuB/H,MAAM,KAAI,CAAC,CAAC;YACjEgI,UAAU,EAAE/E,OAAO,CAACC,aAAa,IAAI;UACvC,CAAC;QAAA,CAAC,CAAC;QACH;QACA,IAAItE,WAAW,IAAI;UAAEA;QAAY,CAAC,CAAC;QACnC,IAAIJ,iBAAiB,GAAG,CAAC,IAAI;UAAEA;QAAkB,CAAC;MACpD,CAAC;MAED+C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiG,MAAM,CAAC;;MAEjC;MACA,MAAMQ,+BAA+B,GAAIC,aAAa,IAAK;QACzD,IAAIA,aAAa,EAAE;UACjB,MAAMvI,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;UACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;YAC3BL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAACkI,aAAa,GAAGA,aAAa;YACnEpI,cAAc,CAACkC,OAAO,CACpB,cAAc,EACdpC,IAAI,CAACiB,SAAS,CAAClB,YAAY,CAC7B,CAAC;UACH;QACF;MACF,CAAC;MACD,IAAI;QACF,IAAIuI,aAAa,GAAG,IAAI;QACxB,MAAMvI,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;QACD,IACEJ,YAAY,CAACK,MAAM,GAAG,CAAC,IACvBL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAACkI,aAAa,EACnD;UACAA,aAAa,GAAGvI,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAACkI,aAAa;QACrE;QACA,MAAMC,QAAQ,GAAG,MAAM/L,SAAS,CAACgM,cAAc,CAAC;UAC9C,GAAGX,MAAM;UACTS;QACF,CAAC,CAAC;QACF3G,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE2G,QAAQ,CAAC;QACrC,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;UAAA,IAAAC,cAAA,EAAAC,qBAAA,EAAAC,qBAAA;UAC5BN,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAG,cAAA,GAARH,QAAQ,CAAEjE,IAAI,cAAAoE,cAAA,wBAAAC,qBAAA,GAAdD,cAAA,CAAgBG,iBAAiB,cAAAF,qBAAA,uBAAjCA,qBAAA,CAAmC9H,GAAG;UACtDwH,+BAA+B,CAACC,aAAa,CAAC;UAC9C,MAAMQ,mBAAmB,GAAGR,aAAa;UACzC,MAAMS,gBAAgB,GAAG,MAAMvM,SAAS,CAACwM,gBAAgB,CACvDF,mBACF,CAAC;UACDnH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEmH,gBAAgB,CAAC;UACrD,MAAME,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAH,qBAAA,GAAhBG,gBAAgB,CAAEzE,IAAI,cAAAsE,qBAAA,uBAAtBA,qBAAA,CAAwBM,UAAU;UACrD,IAAID,UAAU,EAAE;YACd;YACA;YACAE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;UACnC;QACF,CAAC,MAAM,IAAI,CAAAV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;UAAA,IAAAa,eAAA,EAAAC,qBAAA,EAAAC,sBAAA;UACnClB,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAe,eAAA,GAARf,QAAQ,CAAEjE,IAAI,cAAAgF,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBG,WAAW,cAAAF,qBAAA,uBAA3BA,qBAAA,CAA6B1I,GAAG;UAChDwH,+BAA+B,CAACC,aAAa,CAAC;UAC9C,MAAMS,gBAAgB,GAAG,MAAMvM,SAAS,CAACwM,gBAAgB,CACvDV,aACF,CAAC;UACD,MAAMW,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAS,sBAAA,GAAhBT,gBAAgB,CAAEzE,IAAI,cAAAkF,sBAAA,uBAAtBA,sBAAA,CAAwBN,UAAU;UACrD,IAAID,UAAU,EAAE;YACd;YACA;YACAE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;UACnC;QACF,CAAC,MAAM;UACLpM,SAAS,CAACa,KAAK,CAAC,sBAAsB,CAAC;QACzC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QAAA,IAAAgM,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;QAEdlI,OAAO,CAACjE,KAAK,CAAC,wBAAwB,GAAAgM,eAAA,GAAEhM,KAAK,CAAC6K,QAAQ,cAAAmB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBpF,IAAI,cAAAqF,oBAAA,uBAApBA,oBAAA,CAAsBhD,OAAO,CAAC;QACtEP,sBAAsB,CAAC;UACrBC,KAAK,EAAE,eAAe;UACtBC,WAAW,EAAE,6CAA6C;UAC1DC,UAAU,EAAE,EAAAqD,gBAAA,GAAAlM,KAAK,CAAC6K,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtF,IAAI,cAAAuF,qBAAA,uBAApBA,qBAAA,CAAsBlD,OAAO,KAAI;QAC/C,CAAC,CAAC;QACFT,wBAAwB,CAAC,IAAI,CAAC;MAChC;IACF,CAAC,CAAC,OAAOxI,KAAK,EAAE;MACdiE,OAAO,CAACjE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDD,yBAAyB,CAAC,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAMqM,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAM9G,cAAc,GAAG/E,aAAa,CAACgF,MAAM,CACzC,CAACC,KAAK,EAAE;MAAE7B,IAAI;MAAEC;IAAO,CAAC,KAAK4B,KAAK,GAAG7B,IAAI,CAAC8B,KAAK,GAAG7B,MAAM,GAAGyB,YAAY,EACvE,CACF,CAAC;IAED,IAAIC,cAAc,GAAG,CAAC,EAAE;MACtB;MACA,MAAM0E,aAAa,CAAC,CAAC;;MAErB;MACA;MACAnJ,QAAQ,CAAC;QACP+B,IAAI,EAAE5D,aAAa,CAAC6D,mBAAmB;QACvCC,OAAO,EAAE;UACPvC,aAAa,EAAE,EAAE;UACjBE,gBAAgB,EAAE,EAAE;UACpBE,WAAW,EAAEA;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM0L,oBAAoB,GAAGA,CAAA,KAAM;IACjCvE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,IAAI3F,gBAAgB,IAAK,CAACxB,WAAW,IAAI,CAACgB,YAAa,EAAE;IACvD,oBACEnC,OAAA;MACE4H,SAAS,EAAC,kDAAkD;MAC5DkF,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAlF,QAAA,eAE3B7H,OAAA;QAAK4H,SAAS,EAAC,6BAA6B;QAACoF,IAAI,EAAC,QAAQ;QAAAnF,QAAA,eACxD7H,OAAA;UAAM4H,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACjH,WAAW,IAAIgB,YAAY,EAAE;IAChCf,QAAQ,CAAC,CAAC,CAAC,CAAC;IACZ,OAAO,IAAI;EACb;EAEA,oBACEpB,OAAA;IACE4H,SAAS,EAAC,+BAA+B;IACzCkF,KAAK,EAAE;MACLG,eAAe,EAAE,OAAOvO,MAAM,GAAG;MACjCwO,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE;IACtB,CAAE;IAAAtF,QAAA,gBAEF7H,OAAA,CAACrB,MAAM;MAAAsJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVpI,OAAA;MACE4H,SAAS,EAAC,8EAA8E;MACxFkF,KAAK,EAAE;QAAEM,UAAU,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAO,CAAE;MAAAxF,QAAA,gBAErD7H,OAAA,CAACjC,SAAS;QAAC6J,SAAS,EAAC,MAAM;QAAAC,QAAA,eACzB7H,OAAA,CAAChC,GAAG;UAAC4J,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC7H,OAAA,CAACJ,aAAa;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEjBpI,OAAA,CAAC/B,GAAG;YAACqP,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA1F,QAAA,eAChB7H,OAAA,CAAC9B,IAAI;cACH0J,SAAS,EAAC,yBAAyB;cACnCkF,KAAK,EAAE;gBACLU,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfC,YAAY,EAAE;cAChB,CAAE;cAAA9F,QAAA,gBAEF7H,OAAA;gBACE4H,SAAS,EAAC,YAAY;gBACtBkF,KAAK,EAAE;kBACLc,cAAc,EAAE,YAAY;kBAC5BC,YAAY,EAAE;gBAChB,CAAE;gBAAAhG,QAAA,eAEF7H,OAAA,CAAC0H,UAAU;kBAACC,MAAM,EAAExG,WAAW,CAAC2M;gBAAK;kBAAA7F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAENpI,OAAA;gBAAI4H,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAA1H,qBAAA,GAC5BgB,WAAW,CAAC4M,SAAS,cAAA5N,qBAAA,cAAAA,qBAAA,GAAI;cAAE;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAELpI,OAAA;gBAAG4H,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GAAAzH,oBAAA,GACpCe,WAAW,CAAC6M,OAAO,cAAA5N,oBAAA,cAAAA,oBAAA,GAAI;cAAE;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEJpI,OAAA;gBACE4H,SAAS,EAAC,sBAAsB;gBAChCkF,KAAK,EAAE;kBACLC,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPpI,OAAA;gBAAI4H,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAmB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE7CpI,OAAA,CAAChC,GAAG;gBAAC4J,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB7H,OAAA,CAAC/B,GAAG;kBAACiQ,EAAE,EAAE,CAAE;kBAAArG,QAAA,eACT7H,OAAA;oBAAK4H,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACtB7H,OAAA;sBACE4H,SAAS,EAAC,oBAAoB;sBAC9BkF,KAAK,EAAE;wBAAEqB,QAAQ,EAAE;sBAAG,CAAE;sBAAAtG,QAAA,EACzB;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNpI,OAAA;sBAAK4H,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBxI,KAAK,CAAC+O,OAAO,CAAClM,UAAU,CAACmD,WAAW,EAAE,CAAC;oBAAC;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpI,OAAA,CAAC/B,GAAG;kBAACiQ,EAAE,EAAE,CAAE;kBAAArG,QAAA,eACT7H,OAAA;oBAAK4H,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvB7H,OAAA;sBACE4H,SAAS,EAAC,oBAAoB;sBAC9BkF,KAAK,EAAE;wBAAEqB,QAAQ,EAAE;sBAAG,CAAE;sBAAAtG,QAAA,EACzB;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNpI,OAAA;sBAAK4H,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBxI,KAAK,CAAC+O,OAAO,CAAClM,UAAU,CAACqD,YAAY,EAAE,CAAC;oBAAC;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENpI,OAAA;gBAAK4H,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B7H,OAAA;kBAAK4H,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClD7H,OAAA;oBAAA6H,QAAA,EAAM;kBAAqB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClCpI,OAAA;oBAAM4H,SAAS,EAAC,SAAS;oBAAAC,QAAA,GAAEhC,YAAY,EAAC,QAAM;kBAAA;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAAC,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNpI,OAAA;kBAAK4H,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClD7H,OAAA;oBAAA6H,QAAA,EAAM;kBAAuB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpCpI,OAAA;oBAAM4H,SAAS,EAAC,SAAS;oBAAAC,QAAA,GACtB3F,UAAU,CAACmM,MAAM,EAAC,YAAU,EAACnM,UAAU,CAACoM,SAAS,EAAE,GAAG,EAAC,WAE1D;kBAAA;oBAAArG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENpI,OAAA;gBACE4H,SAAS,EAAC,sBAAsB;gBAChCkF,KAAK,EAAE;kBACLC,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPpI,OAAA;gBAAK4H,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjC7H,OAAA;kBAAI4H,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAY;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAErCrH,aAAa,CAACiD,GAAG,CAAC,CAAC;kBAAEG,IAAI;kBAAEC;gBAAO,CAAC,kBAClCpE,OAAA;kBAEE4H,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBAElE7H,OAAA;oBAAA6H,QAAA,GACGzD,MAAM,EAAC,KAAG,EAACD,IAAI,CAACoK,IAAI,EAAC,IAAE,EAAC1I,YAAY,EAAC,SACxC;kBAAA;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPpI,OAAA;oBAAM4H,SAAS,EAAC,SAAS;oBAAAC,QAAA,EACtBxI,KAAK,CAACmP,cAAc,CACnBrK,IAAI,CAAC8B,KAAK,GAAG7B,MAAM,GAAGyB,YACxB;kBAAC;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAVFjE,IAAI,CAACR,GAAG;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACN,CAAC,eAEFpI,OAAA;kBAAK4H,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzB7H,OAAA;oBACE4H,SAAS,EAAC,gCAAgC;oBAC1CkF,KAAK,EAAE;sBAAE2B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAElH,sBAAuB;oBAAAK,QAAA,EACjC;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLnH,gBAAgB,CAACiC,MAAM,GAAG,CAAC,iBAC1BlD,OAAA;gBAAK4H,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC7H,OAAA;kBAAI4H,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAE1CnH,gBAAgB,CAAC+C,GAAG,CAAEmC,OAAO,IAAK;kBACjC,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;kBACjD,MAAMC,eAAe,GACnBF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAClD,MAAM;kBACzC,MAAMyL,YAAY,GAAGxI,OAAO,CAACF,KAAK,GAAGI,eAAe;kBAEpD,oBACErG,OAAA;oBAEE4H,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,gBAElE7H,OAAA;sBAAA6H,QAAA,GACG1B,OAAO,CAACG,QAAQ,EAAC,KAAG,EAACH,OAAO,CAACoI,IAAI,EAAC,IACnC,EAACnI,aAAa,CAAClD,MAAM,EAAC,SACxB;oBAAA;sBAAA+E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPpI,OAAA;sBAAM4H,SAAS,EAAC,SAAS;sBAAAC,QAAA,EACtBxI,KAAK,CAACmP,cAAc,CAACG,YAAY;oBAAC;sBAAA1G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA,GATFjC,OAAO,CAACxC,GAAG;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUb,CAAC;gBAEV,CAAC,CAAC,eAEFpI,OAAA;kBAAK4H,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzB7H,OAAA;oBACE4H,SAAS,EAAC,gCAAgC;oBAC1CkF,KAAK,EAAE;sBAAE2B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAEA,CAAA,KAAM;sBACbrN,QAAQ,CAAC;wBACP+B,IAAI,EAAE5D,aAAa,CAAC6D,mBAAmB;wBACvCC,OAAO,EAAE;0BACPvC,aAAa,EAAEA,aAAa;0BAC5BE,gBAAgB,EAAEA,gBAAgB;0BAClCE,WAAW,EAAEA;wBACf;sBACF,CAAC,CAAC;sBACFC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACd,CAAE;oBAAAyG,QAAA,EACH;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDpI,OAAA;gBACE4H,SAAS,EAAC,sBAAsB;gBAChCkF,KAAK,EAAE;kBACLC,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPpI,OAAA;gBAAK4H,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,GAEpCnG,iBAAiB,GAAG,CAAC,gBACpB1B,OAAA,CAAC9B,IAAI;kBACH0J,SAAS,EAAC,wBAAwB;kBAClCkF,KAAK,EAAE;oBACLU,eAAe,EAAE,wBAAwB;oBACzCoB,WAAW,EAAE,SAAS;oBACtBC,MAAM,EAAE;kBACV,CAAE;kBAAAhH,QAAA,eAEF7H,OAAA,CAAC9B,IAAI,CAAC4Q,IAAI;oBAAClH,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACzB7H,OAAA;sBAAK4H,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChE7H,OAAA;wBAAA6H,QAAA,gBACE7H,OAAA;0BAAK4H,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,gBACxC7H,OAAA,CAACxB,KAAK;4BAACoJ,SAAS,EAAC;0BAAmB;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACvCpI,OAAA;4BAAM4H,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EACnCrG;0BAAa;4BAAAyG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACNpI,OAAA;0BAAO4H,SAAS,EAAC,cAAc;0BAAAC,QAAA,GAAC,OACzB,EAACxI,KAAK,CAACmP,cAAc,CAAC9M,iBAAiB,CAAC;wBAAA;0BAAAuG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNpI,OAAA,CAAC5B,MAAM;wBACL2Q,OAAO,EAAC,gBAAgB;wBACxBC,IAAI,EAAC,IAAI;wBACTN,OAAO,EAAEA,CAAA,KACPpF,6BAA6B,CAAC;0BAC5BrC,IAAI,EAAE,EAAE;0BACRK,QAAQ,EAAE,CAAC;0BACXmC,OAAO,EAAE,EAAE;0BACX3H,WAAW,EAAE;wBACf,CAAC,CACF;wBACD8F,SAAS,EAAC,2BAA2B;wBACrCqH,QAAQ,EACN5M,qBAAqB,IACrBI,kCACD;wBAAAoF,QAAA,gBAED7H,OAAA,CAACvB,OAAO;0BAACmJ,SAAS,EAAC;wBAAM;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EAC3B/F,qBAAqB,IACtBI,kCAAkC,GAC9B,KAAK,GACL,QAAQ;sBAAA;wBAAAwF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,gBAEPpI,OAAA;kBACE4H,SAAS,EAAC,uBAAuB;kBACjCkF,KAAK,EAAE;oBACL+B,MAAM,EAAE,kCAAkC;oBAC1CpB,YAAY,EAAE,KAAK;oBACnBD,eAAe,EAAE;kBACnB,CAAE;kBAAA3F,QAAA,gBAEF7H,OAAA,CAACxB,KAAK;oBAACoJ,SAAS,EAAC,iBAAiB;oBAACoH,IAAI,EAAE;kBAAG;oBAAA/G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/CpI,OAAA;oBAAK4H,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAC;kBAElC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAGDpI,OAAA,CAAC5B,MAAM;kBACL2Q,OAAO,EAAC,eAAe;kBACvBnH,SAAS,EAAC,wDAAwD;kBAClE8G,OAAO,EAAEA,CAAA,KAAMlG,qBAAqB,CAAC,IAAI,CAAE;kBAC3CsE,KAAK,EAAE;oBACLoC,WAAW,EAAE,QAAQ;oBACrBC,WAAW,EAAE,KAAK;oBAClBzB,OAAO,EAAE;kBACX,CAAE;kBACFuB,QAAQ,EACN5M,qBAAqB,IACrBI,kCACD;kBAAAoF,QAAA,gBAED7H,OAAA,CAACxB,KAAK;oBAACoJ,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACzB/F,qBAAqB,IAAII,kCAAkC,GACxD,eAAe,GACff,iBAAiB,GAAG,CAAC,GACrB,kBAAkB,GAClB,kBAAkB;gBAAA;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,EAGR,CAAC/F,qBAAqB,IACrBI,kCAAkC,kBAClCzC,OAAA;kBAAK4H,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/B7H,OAAA;oBAAO4H,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBAC1B7H,OAAA;sBACE4H,SAAS,EAAC,uCAAuC;sBACjDoF,IAAI,EAAC,QAAQ;sBAAAnF,QAAA,eAEb7H,OAAA;wBAAM4H,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,EAAC;sBAAU;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,kCAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNpI,OAAA;gBAAK4H,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B7H,OAAA;kBAAK4H,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrE7H,OAAA;oBAAA6H,QAAA,EAAM;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtBpI,OAAA;oBAAM4H,SAAS,EAAC,SAAS;oBAAAC,QAAA,EACtBxI,KAAK,CAACmP,cAAc,CAACjI,QAAQ;kBAAC;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EAEL1G,iBAAiB,GAAG,CAAC,iBACpB1B,OAAA;kBAAK4H,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrE7H,OAAA;oBAAM4H,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/CpI,OAAA;oBAAM4H,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,GAAC,GACpC,EAACxI,KAAK,CAACmP,cAAc,CAAC9M,iBAAiB,CAAC;kBAAA;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACN,eAEDpI,OAAA;kBACE4H,SAAS,EAAC,sBAAsB;kBAChCkF,KAAK,EAAE;oBACLC,MAAM,EAAE,KAAK;oBACbS,eAAe,EAAE,uBAAuB;oBACxCS,MAAM,EAAE;kBACV;gBAAE;kBAAAhG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEPpI,OAAA;kBAAK4H,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAChE7H,OAAA;oBAAI4H,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAAC,SACxB,EAACxI,KAAK,CAACmP,cAAc,CAAChI,UAAU,CAAC;kBAAA;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNpI,OAAA;kBAAK4H,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNpI,OAAA,CAAC/B,GAAG;YAACqP,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA1F,QAAA,eAChB7H,OAAA,CAAC9B,IAAI;cACH0J,SAAS,EAAC,WAAW;cACrBkF,KAAK,EAAE;gBACLU,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACf0B,KAAK,EAAE;cACT,CAAE;cAAAvH,QAAA,gBAEF7H,OAAA;gBAAI4H,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAsB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEhDpI,OAAA,CAAC7B,IAAI;gBAAA0J,QAAA,gBACH7H,OAAA,CAAC7B,IAAI,CAACkR,KAAK;kBAACzH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1B7H,OAAA,CAAC7B,IAAI,CAACmR,KAAK;oBAAAzH,QAAA,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClCpI,OAAA,CAAC7B,IAAI,CAACoR,OAAO;oBACXnM,IAAI,EAAC,MAAM;oBACXoM,KAAK,EAAE9O,IAAI,CAAC6N,IAAK;oBACjB3G,SAAS,EAAC,2BAA2B;oBACrCkF,KAAK,EAAE;sBACL+B,MAAM,EAAE,iCAAiC;sBACzCpB,YAAY,EAAE;oBAChB;kBAAE;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbpI,OAAA,CAAC7B,IAAI,CAACkR,KAAK;kBAACzH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1B7H,OAAA,CAAC7B,IAAI,CAACmR,KAAK;oBAAAzH,QAAA,EAAC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BpI,OAAA,CAAC7B,IAAI,CAACoR,OAAO;oBACXnM,IAAI,EAAC,OAAO;oBACZoM,KAAK,EAAE9O,IAAI,CAAC+O,KAAM;oBAClBC,WAAW,EAAC,sBAAsB;oBAClC9H,SAAS,EAAC,2BAA2B;oBACrCkF,KAAK,EAAE;sBACL+B,MAAM,EAAE,iCAAiC;sBACzCpB,YAAY,EAAE;oBAChB;kBAAE;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbpI,OAAA,CAAC7B,IAAI,CAACkR,KAAK;kBAACzH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1B7H,OAAA,CAAC7B,IAAI,CAACmR,KAAK;oBAAAzH,QAAA,EAAC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BpI,OAAA,CAAC7B,IAAI,CAACoR,OAAO;oBACXnM,IAAI,EAAC,KAAK;oBACVoM,KAAK,EAAE9O,IAAI,CAACiP,WAAY;oBACxBD,WAAW,EAAC,YAAY;oBACxB9H,SAAS,EAAC,2BAA2B;oBACrCkF,KAAK,EAAE;sBACL+B,MAAM,EAAE,iCAAiC;sBACzCpB,YAAY,EAAE;oBAChB;kBAAE;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbpI,OAAA,CAAC7B,IAAI,CAACkR,KAAK;kBAACzH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1B7H,OAAA,CAAC7B,IAAI,CAACmR,KAAK;oBAAAzH,QAAA,EAAC;kBAAwB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjDpI,OAAA;oBAAA6H,QAAA,gBACE7H,OAAA,CAAC7B,IAAI,CAACyR,KAAK;sBACTxM,IAAI,EAAC,OAAO;sBACZyM,EAAE,EAAC,WAAW;sBACdC,KAAK,EAAC,oBAAoB;sBAC1BvB,IAAI,EAAC,YAAY;sBACjBwB,OAAO,EAAEzO,UAAU,KAAK,WAAY;sBACpC0O,QAAQ,EAAEA,CAAA,KAAMzO,aAAa,CAAC,WAAW,CAAE;sBAC3CqG,SAAS,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACFpI,OAAA,CAAC7B,IAAI,CAACyR,KAAK;sBACTxM,IAAI,EAAC,OAAO;sBACZyM,EAAE,EAAC,aAAa;sBAChBC,KAAK,EAAC,8BAA8B;sBACpCvB,IAAI,EAAC,YAAY;sBACjBwB,OAAO,EAAEzO,UAAU,KAAK,aAAc;sBACtC0O,QAAQ,EAAEA,CAAA,KAAMzO,aAAa,CAAC,aAAa;oBAAE;sBAAA0G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbpI,OAAA;kBAAK4H,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1B7H,OAAA,CAAC5B,MAAM;oBACLwJ,SAAS,EAAC,WAAW;oBACrBkF,KAAK,EAAE;sBACLW,YAAY,EAAE,MAAM;sBACpBD,eAAe,EAAE,OAAO;sBACxB4B,KAAK,EAAE,SAAS;sBAChBP,MAAM,EAAE,MAAM;sBACdoB,UAAU,EAAE;oBACd,CAAE;oBACFvB,OAAO,EAAE7B,oBAAqB;oBAC9BoC,QAAQ,EACN1M,qBAAqB,IACrBF,qBAAqB,IACrBI,kCACD;oBAAAoF,QAAA,EAEApF,kCAAkC,GAC/B,yBAAyB,GACzBF,qBAAqB,GACrB,mBAAmB,GACnB;kBAAS;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eAETpI,OAAA,CAACjB,iBAAiB;oBAChBmR,IAAI,EAAE7H,eAAgB;oBACtB8H,MAAM,EAAEA,CAAA,KAAM7H,kBAAkB,CAAC,KAAK,CAAE;oBACxC8H,SAAS,EAAExD,YAAa;oBACxBzD,KAAK,EAAC,oBAAoB;oBAC1BM,OAAO,EAAC,wDAAwD;oBAChE4G,iBAAiB,EAAC,QAAQ;oBAC1BjN,IAAI,EAAC;kBAAQ;oBAAA6E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACZpI,OAAA;QAAA6H,QAAA,eACE7H,OAAA,CAACT,OAAO;UAAA0I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNpI,OAAA,CAACpB,MAAM;MAAAqJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVpI,OAAA,CAAChB,cAAc;MACbkR,IAAI,EAAE3H,kBAAmB;MACzB4H,MAAM,EAAEA,CAAA,KAAM3H,qBAAqB,CAAC,KAAK,CAAE;MAC3CsC,UAAU,EAAEvE,QAAS;MACrB+J,gBAAgB,EAAEhH,6BAA8B;MAChDiH,kBAAkB,EAAEzO;IAAY;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEFpI,OAAA,CAACN,gBAAgB;MACfwQ,IAAI,EAAE5P,sBAAuB;MAC7BkQ,OAAO,EAAEA,CAAA,KAAM;QACbjQ,yBAAyB,CAAC,KAAK,CAAC;MAClC;IAAE;MAAA0H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFpI,OAAA,CAACf,mBAAmB;MAClBiR,IAAI,EAAEzH,uBAAwB;MAC9B+H,OAAO,EAAEA,CAAA,KAAM;QACb9H,0BAA0B,CAAC,KAAK,CAAC;QACjCE,wBAAwB,CAAC,EAAE,CAAC;QAC5BE,uBAAuB,CAAC,EAAE,CAAC;MAC7B,CAAE;MACF2H,oBAAoB,EAAEA,CAAA,KAAM;QAC1B/H,0BAA0B,CAAC,KAAK,CAAC;QACjCE,wBAAwB,CAAC,EAAE,CAAC;QAC5BE,uBAAuB,CAAC,EAAE,CAAC;QAC3BN,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAE;MACFkI,YAAY,EAAE/H,qBAAsB;MACpCnH,aAAa,EAAEqH;IAAqB;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAEFpI,OAAA,CAACF,eAAe;MACdoQ,IAAI,EAAEnH,qBAAsB;MAC5ByH,OAAO,EAAEA,CAAA,KAAMxH,wBAAwB,CAAC,KAAK,CAAE;MAC/CG,KAAK,EAAEF,mBAAmB,CAACE,KAAM;MACjCC,WAAW,EAAEH,mBAAmB,CAACG,WAAY;MAC7CC,UAAU,EAAEJ,mBAAmB,CAACI,UAAW;MAC3CsH,UAAU,EAAC;IAAW;MAAA1I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAClI,EAAA,CAlkCID,gBAAgB;EAAA,QAKPf,cAAc,EACDA,cAAc,EAIbA,cAAc,EAGPA,cAAc,EAGnBA,cAAc,EAG1BJ,WAAW,EACXK,cAAc;AAAA;AAAAyR,EAAA,GApB3B3Q,gBAAgB;AAokCtB,eAAeA,gBAAgB;AAAC,IAAA2Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
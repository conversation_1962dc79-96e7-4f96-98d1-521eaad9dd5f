{"ast": null, "code": "import ApiConstants from \"../../adapter/ApiConstants\";\nimport api from \"../../libs/api/index\";\nconst Factories = {\n  fetchUserPromotions: () => {\n    return api.get(ApiConstants.FETCH_USER_PROMOTIONS);\n  },\n  // Fetch all promotions for modal (public endpoint)\n  fetchAllPromotions: totalPrice => {\n    const params = totalPrice ? `?totalPrice=${totalPrice}` : '';\n    return api.get(`/api/promotions${params}`);\n  },\n  // Apply promotion\n  applyPromotion: data => {\n    return api.post(ApiConstants.USE_PROMOTION, data);\n  },\n  // Legacy method (keep for backward compatibility)\n  usePromotion: data => {\n    return api.post(ApiConstants.USE_PROMOTION, data);\n  }\n};\nexport default Factories;", "map": {"version": 3, "names": ["ApiConstants", "api", "Factories", "fetchUserPromotions", "get", "FETCH_USER_PROMOTIONS", "fetchAllPromotions", "totalPrice", "params", "applyPromotion", "data", "post", "USE_PROMOTION", "usePromotion"], "sources": ["E:/Uroom/Customer/src/redux/promotion/factories.js"], "sourcesContent": ["import ApiConstants from \"../../adapter/ApiConstants\";\r\nimport api from \"../../libs/api/index\";\r\n\r\nconst Factories = {\r\n  fetchUserPromotions: () => {\r\n    return api.get(ApiConstants.FETCH_USER_PROMOTIONS);\r\n  },\r\n\r\n  // Fetch all promotions for modal (public endpoint)\r\n  fetchAllPromotions: (totalPrice) => {\r\n    const params = totalPrice ? `?totalPrice=${totalPrice}` : '';\r\n    return api.get(`/api/promotions${params}`);\r\n  },\r\n\r\n  // Apply promotion\r\n  applyPromotion: (data) => {\r\n    return api.post(ApiConstants.USE_PROMOTION, data);\r\n  },\r\n\r\n  // Legacy method (keep for backward compatibility)\r\n  usePromotion: (data) => {\r\n    return api.post(ApiConstants.USE_PROMOTION, data);\r\n  },\r\n};\r\n\r\nexport default Factories;\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,OAAOC,GAAG,MAAM,sBAAsB;AAEtC,MAAMC,SAAS,GAAG;EAChBC,mBAAmB,EAAEA,CAAA,KAAM;IACzB,OAAOF,GAAG,CAACG,GAAG,CAACJ,YAAY,CAACK,qBAAqB,CAAC;EACpD,CAAC;EAED;EACAC,kBAAkB,EAAGC,UAAU,IAAK;IAClC,MAAMC,MAAM,GAAGD,UAAU,GAAG,eAAeA,UAAU,EAAE,GAAG,EAAE;IAC5D,OAAON,GAAG,CAACG,GAAG,CAAC,kBAAkBI,MAAM,EAAE,CAAC;EAC5C,CAAC;EAED;EACAC,cAAc,EAAGC,IAAI,IAAK;IACxB,OAAOT,GAAG,CAACU,IAAI,CAACX,YAAY,CAACY,aAAa,EAAEF,IAAI,CAAC;EACnD,CAAC;EAED;EACAG,YAAY,EAAGH,IAAI,IAAK;IACtB,OAAOT,GAAG,CAACU,IAAI,CAACX,YAAY,CAACY,aAAa,EAAEF,IAAI,CAAC;EACnD;AACF,CAAC;AAED,eAAeR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
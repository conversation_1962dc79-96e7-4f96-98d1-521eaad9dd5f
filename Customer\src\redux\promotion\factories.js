import ApiConstants from "../../adapter/ApiConstants";
import api from "../../libs/api/index";

const Factories = {
  fetchUserPromotions: () => {
    return api.get(ApiConstants.FETCH_USER_PROMOTIONS);
  },

  // Fetch all promotions for modal (public endpoint)
  fetchAllPromotions: (totalPrice) => {
    const params = totalPrice ? `?totalPrice=${totalPrice}` : '';
    return api.get(`/api/promotions${params}`);
  },

  // Apply promotion
  applyPromotion: (data) => {
    return api.post(ApiConstants.USE_PROMOTION, data);
  },

  // Legacy method (keep for backward compatibility)
  usePromotion: (data) => {
    return api.post(ApiConstants.USE_PROMOTION, data);
  },
};

export default Factories;

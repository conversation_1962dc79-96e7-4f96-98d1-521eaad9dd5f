# Test Plan: Promotion Redux Migration

## Overview
Đã chuyển đổi promotion functionality từ axios sang Redux trong:
- PromotionModal component
- BookingCheckPage component

## Changes Made

### 1. Redux Actions (Customer/src/redux/promotion/actions.js)
- ✅ Added `FETCH_ALL_PROMOTIONS` actions for modal
- ✅ Added `APPLY_PROMOTION` actions for applying promotions
- ✅ Kept legacy `USE_PROMOTION` actions for backward compatibility

### 2. Redux Factories (Customer/src/redux/promotion/factories.js)
- ✅ Added `fetchAllPromotions()` method for modal
- ✅ Updated `applyPromotion()` method
- ✅ Kept legacy `usePromotion()` method

### 3. Redux Saga (Customer/src/redux/promotion/saga.js)
- ✅ Added `fetchAllPromotions` saga handler
- ✅ Added `applyPromotion` saga handler
- ✅ Updated imports and exports

### 4. Redux Reducer (Customer/src/redux/promotion/reducer.js)
- ✅ Added state for modal promotions (`allPromotions`, `allPromotionsLoading`, `allPromotionsError`)
- ✅ Added state for apply promotion (`applyLoading`, `applyError`, `appliedPromotion`)
- ✅ Added reducer cases for new actions

### 5. PromotionModal Component
- ✅ Replaced axios with Redux actions
- ✅ Added Redux selectors for state management
- ✅ Updated promotion validation logic
- ✅ Added error handling and retry functionality

### 6. BookingCheckPage Component
- ✅ Replaced axios calls in promotion validation
- ✅ Updated `validatePromotionBeforeBooking` function
- ✅ Added Redux selectors
- ✅ Removed axios import

## Test Cases

### Test 1: PromotionModal Functionality
1. **Open promotion modal** from BookingCheckPage
2. **Verify loading state** shows while fetching promotions
3. **Check promotion list** displays correctly
4. **Apply promotion** and verify it works
5. **Error handling** - test with network issues

### Test 2: BookingCheckPage Promotion Validation
1. **Apply promotion** via modal
2. **Change booking details** (rooms, dates) and verify promotion re-validation
3. **Navigate to booking** and verify promotion validation before booking
4. **Test error scenarios** - invalid promotions, network errors

### Test 3: Redux State Management
1. **Check Redux DevTools** for proper action dispatching
2. **Verify state updates** in promotion reducer
3. **Test loading states** are properly managed
4. **Ensure no memory leaks** or unnecessary re-renders

## Expected Behavior

### PromotionModal
- Should fetch promotions using Redux when opened
- Should show loading spinner during fetch
- Should display error message with retry button on failure
- Should apply promotions using Redux actions
- Should close modal after successful application

### BookingCheckPage
- Should validate promotions using Redux when booking details change
- Should show validation loading states
- Should handle promotion validation before booking
- Should clear invalid promotions automatically

## Regression Tests
- ✅ Ensure existing promotion functionality still works
- ✅ Verify promotion codes can still be applied manually
- ✅ Check promotion discount calculations are correct
- ✅ Ensure promotion persistence in sessionStorage works
- ✅ Verify promotion removal functionality

## Notes
- All axios calls have been replaced with Redux actions
- Error handling has been improved with proper user feedback
- Loading states are properly managed
- Backward compatibility maintained with legacy actions
